import { useState, useEffect, useC<PERSON>back, useMemo, memo } from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { CustomBadge } from "@/components/ui/custom-badge";
import { Skeleton } from "@/components/ui/skeleton";
import { FormMeta, FormStatus } from "@/lib/schemas/form-schemas";
import { FormService } from "@/lib/services/form-service";
import { PlusCircle, Search, Calendar, FileText } from "lucide-react";

export default function FormListPage() {
  const [forms, setForms] = useState<FormMeta[]>([]);
  const [filteredForms, setFilteredForms] = useState<FormMeta[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<"all" | FormStatus>("all");
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    const loadForms = async () => {
      setIsLoading(true);
      try {
        const data = await FormService.getForms();
        setForms(data);
        setFilteredForms(data);
      } catch (error) {
        console.error("Failed to load forms:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadForms();
  }, []);

  // Memoize the filtered forms to avoid unnecessary re-filtering
  const filteredFormsData = useMemo(() => {
    let result = [...forms];

    // Filter by status
    if (activeTab !== "all") {
      result = result.filter((form) => form.status === activeTab);
    }

    // Filter by search query
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        (form) =>
          form.name.toLowerCase().includes(query) ||
          form.description?.toLowerCase().includes(query)
      );
    }

    return result;
  }, [forms, activeTab, searchQuery]);

  useEffect(() => {
    setFilteredForms(filteredFormsData);
  }, [filteredFormsData]);

  const handleTabChange = useCallback((value: string) => {
    setActiveTab(value as "all" | FormStatus);
  }, []);

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(e.target.value);
    },
    []
  );

  const formatDate = useCallback((dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  }, []);

  const formatTime = useCallback((dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    }).format(date);
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Forms</h1>
        <Button asChild size="sm" className="sm:w-auto">
          <Link to="/forms/new">
            <PlusCircle className="mr-2 h-4 w-4" />
            New Form
          </Link>
        </Button>
      </div>

      <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search forms..."
            className="pl-8"
            value={searchQuery}
            onChange={handleSearchChange}
          />
        </div>
        <Tabs
          defaultValue="all"
          onValueChange={handleTabChange}
          className="w-full sm:w-auto"
        >
          <TabsList className="grid w-full grid-cols-3 sm:w-auto">
            <TabsTrigger value="all">All Forms</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="draft">Drafts</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {renderContent()}
    </div>
  );

  // Extract the rendering logic to a separate function for clarity
  function renderContent() {
    if (isLoading) {
      return <FormListSkeleton />;
    }

    if (filteredForms.length === 0) {
      return <EmptyState searchQuery={searchQuery} activeTab={activeTab} />;
    }

    // Find the showcase form
    const showcaseForm = filteredForms.find((form) => form.id === "5");
    const regularForms = filteredForms.filter((form) => form.id !== "5");

    return (
      <div className="space-y-8">
        {/* Showcase form section */}
        {showcaseForm && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Featured Example</h2>
            <div className="rounded-lg border-2 border-primary/20 bg-primary/5 p-4">
              <div className="mb-4">
                <p className="text-sm text-muted-foreground">
                  This example demonstrates all the capabilities of the form
                  builder, including multi-step forms, conditional rendering,
                  validation rules, and data grids.
                </p>
              </div>
              <FormCard
                key={showcaseForm.id}
                form={showcaseForm}
                formatDate={formatDate}
                formatTime={formatTime}
                isShowcase={true}
              />
            </div>
          </div>
        )}

        {/* Regular forms section */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Your Forms</h2>
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {regularForms.map((form) => (
              <FormCard
                key={form.id}
                form={form}
                formatDate={formatDate}
                formatTime={formatTime}
              />
            ))}
          </div>
        </div>
      </div>
    );
  }
}

// Memoized FormCard component for better performance
const FormCard = memo(
  ({
    form,
    formatDate,
    formatTime,
    isShowcase = false,
  }: {
    form: FormMeta;
    formatDate: (date: string) => string;
    formatTime: (date: string) => string;
    isShowcase?: boolean;
  }) => {
    return (
      <Card
        className={`transition-all hover:shadow-md ${
          isShowcase ? "border-primary/30" : ""
        }`}
      >
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>{form.name}</CardTitle>
            {isShowcase && (
              <CustomBadge
                variant="default"
                className="bg-primary text-primary-foreground"
              >
                Example
              </CustomBadge>
            )}
          </div>
          <CardDescription>
            {form.description ?? "No description"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center text-muted-foreground">
                <Calendar className="mr-1 h-3.5 w-3.5" />
                <span>Last updated:</span>
              </div>
              <div className="flex items-center">
                <span>{formatDate(form.updatedAt)}</span>
                <span className="mx-1 text-muted-foreground">at</span>
                <span>{formatTime(form.updatedAt)}</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Status:</span>
              <CustomBadge
                variant={form.status === "active" ? "success" : "warning"}
                className="capitalize"
              >
                {form.status}
              </CustomBadge>
            </div>
            {isShowcase && (
              <div className="mt-2 pt-2 border-t text-sm text-muted-foreground">
                <ul className="list-disc list-inside space-y-1">
                  <li>Multi-step form with 4 steps</li>
                  <li>Collapsible sections</li>
                  <li>Conditional rendering</li>
                  <li>Validation rules</li>
                  <li>Data grid with different input types</li>
                </ul>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter>
          <Button
            asChild
            variant={isShowcase ? "default" : "outline"}
            className="w-full"
          >
            <Link to={`/forms/${form.id}`}>
              <FileText className="mr-2 h-4 w-4" />
              {isShowcase ? "Explore Example" : "Edit Form"}
            </Link>
          </Button>
        </CardFooter>
      </Card>
    );
  }
);

FormCard.displayName = "FormCard";

// Skeleton loader for the form list
// Generate static skeleton card keys
const skeletonCardKeys = Array.from(
  { length: 6 },
  (_, i) => `skeleton-card-${i}`
);

function FormListSkeleton() {
  return (
    <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
      {skeletonCardKeys.map((key) => (
        <Card key={key} className="overflow-hidden">
          <CardHeader className="pb-2">
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="mt-2 h-4 w-full" />
          </CardHeader>
          <CardContent className="pb-2">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-1/3" />
                <Skeleton className="h-4 w-1/3" />
              </div>
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-1/4" />
                <Skeleton className="h-5 w-16 rounded-full" />
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Skeleton className="h-9 w-full" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );
}

// Helper function to get the appropriate message based on the active tab
function getEmptyStateMessage(activeTab: string): string {
  if (activeTab === "active") {
    return "You don't have any active forms. Activate a draft form to see it here.";
  } else if (activeTab === "draft") {
    return "You don't have any draft forms. Create a new form to get started.";
  } else {
    return "You don't have any forms yet. Create your first form to get started.";
  }
}

// Empty state component
function EmptyState({
  searchQuery,
  activeTab,
}: Readonly<{
  searchQuery: string;
  activeTab: string;
}>) {
  return (
    <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
      <div className="flex h-20 w-20 items-center justify-center rounded-full bg-muted">
        <FileText className="h-10 w-10 text-muted-foreground" />
      </div>
      <h3 className="mt-4 text-lg font-semibold">No forms found</h3>
      {searchQuery ? (
        <p className="mt-2 text-sm text-muted-foreground">
          No forms match your search query "{searchQuery}".
          {activeTab !== "all" && ` Try searching in all forms.`}
        </p>
      ) : (
        <p className="mt-2 text-sm text-muted-foreground">
          {getEmptyStateMessage(activeTab)}
        </p>
      )}
      <Button asChild className="mt-4">
        <Link to="/forms/new">
          <PlusCircle className="mr-2 h-4 w-4" />
          Create New Form
        </Link>
      </Button>
    </div>
  );
}

import { useEffect, useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { DynamicDetailsPage } from "@/components/dynamic-page/DynamicDetailsPage";
import { FormPageConfig } from "@/lib/types/page-config";
import { Loading } from "@/components/ui/loading";
import EditEntityDialog from "@/components/dynamic-page/EditEntityDialog";
import { createFormPageConfig } from "@/lib/config/entity-config-registry";

/**
 * Page component for displaying entity details
 */
export default function DynamicDetailsPageRoute() {
  const { entityName, id } = useParams<{ entityName: string; id: string }>();
  const navigate = useNavigate();
  const [showEditForm, setShowEditForm] = useState(false);
  const [config, setConfig] = useState<FormPageConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Load the appropriate config for this entity type
  useEffect(() => {
    const loadConfig = async () => {
      if (!entityName) {
        navigate("/");
        return;
      }

      setIsLoading(true);
      try {
        // Get the form configuration from the registry
        const formConfig = createFormPageConfig(entityName);

        if (formConfig) {
          setConfig(formConfig);
        } else {
          console.error(`No config found for entity type: ${entityName}`);
          navigate("/");
        }
      } catch (error) {
        console.error("Error loading config:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadConfig();
  }, [entityName, navigate]);

  // Handle edit action
  const handleEdit = () => {
    setShowEditForm(true);
  };

  // Handle form submission success
  const handleFormSuccess = () => {
    setShowEditForm(false);
    // Reload the page to show updated data
    window.location.reload();
  };

  if (isLoading) {
    return <Loading message="Loading details..." />;
  }

  if (!config || !id) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <h2 className="text-2xl font-bold mb-4">Configuration Error</h2>
        <p className="text-muted-foreground mb-6">
          Could not load the configuration for this entity type.
        </p>
      </div>
    );
  }

  // Determine the back button URL based on the entity type
  const getBackButtonUrl = () => {
    if (entityName?.toLowerCase() === "fundinground") {
      return "/funding-rounds";
    }
    return "/projects";
  };

  return (
    <>
      <DynamicDetailsPage
        config={config}
        entityId={id}
        onEdit={handleEdit}
        backButtonUrl={getBackButtonUrl()}
      />

      {/* Edit Form Dialog */}
      <EditEntityDialog
        open={showEditForm}
        onOpenChange={setShowEditForm}
        config={config}
        entityId={id}
        onSuccess={handleFormSuccess}
      />
    </>
  );
}

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  FormSubmission,
  SubmissionMeta,
  SubmissionStatus,
} from "@/lib/types/submission";
import { queryKeys } from "@/lib/types/api";
import { useApiSwitch, ApiType } from "./useApiSwitch";
import { useToast } from "@/components/ui/use-toast";

// Type guard to check if we're using the mock API
function isMockApi(
  api: ApiType
): api is typeof import("@/lib/api/mock-api").mockApi {
  return "getSubmissions" in api;
}

// Type guard to check if we're using the real API
function isRealApi(
  api: ApiType
): api is typeof import("@/lib/api/api-client").apiClient {
  return "get" in api;
}

/**
 * Hook for fetching all submissions with optional filtering
 * This hook uses either the mock API or real API based on the current mode
 */
export function useSubmissions(
  status?: SubmissionStatus,
  applicantId?: string
) {
  const { api, isMockMode } = useApiSwitch();
  const { toast } = useToast();

  const queryKey = [...queryKeys.submissions(), { status, applicantId }];

  return useQuery({
    queryKey,
    queryFn: async () => {
      try {
        if (isMockMode && isMockApi(api)) {
          // Use mock API
          return await api.getSubmissions(status, applicantId);
        } else if (isRealApi(api)) {
          // Use real API
          const response = await api.get<SubmissionMeta[]>("/submissions", {
            params: { status, applicantId },
          });
          return response.data;
        } else {
          throw new Error("Invalid API configuration");
        }
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error
              ? error.message
              : "Failed to fetch submissions",
          variant: "destructive",
        });
        throw error;
      }
    },
    staleTime: 1 * 60 * 1000, // 1 minute
  });
}

/**
 * Hook for fetching a single submission by ID
 */
export function useSubmission(id: string) {
  const { api, isMockMode } = useApiSwitch();
  const { toast } = useToast();

  return useQuery({
    queryKey: queryKeys.submission(id),
    queryFn: async () => {
      try {
        if (isMockMode && isMockApi(api)) {
          // Use mock API
          const submission = await api.getSubmissionById(id);
          if (!submission) {
            throw new Error(`Submission with ID ${id} not found`);
          }
          return submission;
        } else if (isRealApi(api)) {
          // Use real API
          const response = await api.get<FormSubmission>(`/submissions/${id}`);
          return response.data;
        } else {
          throw new Error("Invalid API configuration");
        }
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error
              ? error.message
              : "Failed to fetch submission",
          variant: "destructive",
        });
        throw error;
      }
    },
    enabled: !!id,
  });
}

/**
 * Hook for creating a new submission
 */
export function useCreateSubmission() {
  const { api, isMockMode } = useApiSwitch();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (submissionData: Partial<FormSubmission>) => {
      try {
        if (isMockMode && isMockApi(api)) {
          // Use mock API
          return await api.createSubmission(submissionData);
        } else if (isRealApi(api)) {
          // Use real API
          const response = await api.post<FormSubmission>(
            "/submissions",
            submissionData
          );
          return response.data;
        } else {
          throw new Error("Invalid API configuration");
        }
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error
              ? error.message
              : "Failed to create submission",
          variant: "destructive",
        });
        throw error;
      }
    },
    onSuccess: (data) => {
      // Invalidate submissions list queries when a new submission is created
      queryClient.invalidateQueries({ queryKey: queryKeys.submissions() });

      // If the submission has a form ID, invalidate that form's submissions
      if (data.formId) {
        queryClient.invalidateQueries({
          queryKey: [...queryKeys.forms(), data.formId, "submissions"],
        });
      }

      toast({
        title: "Success",
        description: "Submission created successfully",
      });
    },
  });
}

/**
 * Hook for updating an existing submission
 */
export function useUpdateSubmission(id: string) {
  const { api, isMockMode } = useApiSwitch();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (submissionData: Partial<FormSubmission>) => {
      try {
        if (isMockMode && isMockApi(api)) {
          // Use mock API
          return await api.updateSubmission(id, submissionData);
        } else if (isRealApi(api)) {
          // Use real API
          const response = await api.put<FormSubmission>(
            `/submissions/${id}`,
            submissionData
          );
          return response.data;
        } else {
          throw new Error("Invalid API configuration");
        }
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error
              ? error.message
              : "Failed to update submission",
          variant: "destructive",
        });
        throw error;
      }
    },
    onSuccess: (data) => {
      // Invalidate both the submissions list and the specific submission
      queryClient.invalidateQueries({ queryKey: queryKeys.submissions() });
      queryClient.invalidateQueries({ queryKey: queryKeys.submission(id) });

      // If the submission has a form ID, invalidate that form's submissions
      if (data.formId) {
        queryClient.invalidateQueries({
          queryKey: [...queryKeys.forms(), data.formId, "submissions"],
        });
      }

      toast({
        title: "Success",
        description: "Submission updated successfully",
      });
    },
  });
}

/**
 * Hook for deleting a submission
 */
export function useDeleteSubmission() {
  const { api, isMockMode } = useApiSwitch();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        if (isMockMode && isMockApi(api)) {
          // Use mock API
          await api.deleteSubmission(id);
        } else if (isRealApi(api)) {
          // Use real API
          await api.delete(`/submissions/${id}`);
        } else {
          throw new Error("Invalid API configuration");
        }
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error
              ? error.message
              : "Failed to delete submission",
          variant: "destructive",
        });
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidate submissions list queries when a submission is deleted
      queryClient.invalidateQueries({ queryKey: queryKeys.submissions() });

      toast({
        title: "Success",
        description: "Submission deleted successfully",
      });
    },
  });
}

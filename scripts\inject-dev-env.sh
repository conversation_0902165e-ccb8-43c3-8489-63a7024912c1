#!/bin/sh

# Development environment injection script
# This script creates the env.js file with environment variables for development mode

echo "Injecting development environment variables..."

# Ensure the public directory exists
mkdir -p /app/public

# Create the env.js file with development environment variables
cat > /app/public/env.js << EOF
// Environment variables for development mode
window.ENV = {
  VITE_API_MODE: "${VITE_API_MODE:-real}",
  VITE_API_BASE_URL: "${VITE_API_BASE_URL:-http://**********:8080/api/v1}",
  VITE_COGNITO_HOSTED_UI_URL: "${VITE_COGNITO_HOSTED_UI_URL:-}",
  VITE_COGNITO_CLIENT_ID: "${VITE_COGNITO_CLIENT_ID:-}",
  VITE_COGNITO_USER_POOL_ID: "${VITE_COGNITO_USER_POOL_ID:-}",
  VITE_COGNITO_REGION: "${VITE_COGNITO_REGION:-}",
  VITE_COGNITO_REDIRECT_URI: "${VITE_COGNITO_REDIRECT_URI:-http://localhost:3000/callback}"
};
EOF

echo "Development environment variables injected into /app/public/env.js"

# Execute the original command
exec "$@"

# Docker environment variables
# Copy this file to .env.local and customize for your environment

VITE_API_MODE=real
VITE_API_BASE_URL=http://**********:8080/api/v1

# AWS Cognito Configuration
VITE_COGNITO_HOSTED_UI_URL=https://eu-west-20cgybcvhe.auth.eu-west-2.amazoncognito.com
VITE_COGNITO_CLIENT_ID=3u7k5r2rp5thlmlt3031aa5fh3
VITE_COGNITO_USER_POOL_ID=eu-west-2_0CgYbCVhE
VITE_COGNITO_REGION=eu-west-2
VITE_COGNITO_REDIRECT_URI=http://localhost:3000/callback

# Production overrides (uncomment and modify for production)
# VITE_API_URL=https://your-api-domain.com/api/v1
# VITE_COGNITO_REDIRECT_URI=https://your-domain.com/callback

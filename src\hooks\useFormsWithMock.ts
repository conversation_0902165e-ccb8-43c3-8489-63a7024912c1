import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { FormMeta, FormSchema, FormStatus } from "@/lib/types/form";
import { queryKeys } from "@/lib/types/api";
import { useApiSwitch, ApiType } from "./useApiSwitch";
import { useToast } from "@/components/ui/use-toast";

// Type guard to check if we're using the mock API
function isMockApi(
  api: ApiType
): api is typeof import("@/lib/api/mock-api").mockApi {
  return "getForms" in api;
}

// Type guard to check if we're using the real API
function isRealApi(
  api: ApiType
): api is typeof import("@/lib/api/api-client").apiClient {
  return "get" in api;
}

/**
 * Hook for fetching all forms with optional filtering
 * This hook uses either the mock API or real API based on the current mode
 */
export function useForms(status?: FormStatus) {
  const { api, isMockMode } = useApiSwitch();
  const { toast } = useToast();

  const queryKey = status
    ? [...queryKeys.forms(), { status }]
    : queryKeys.forms();

  return useQuery({
    queryKey,
    queryFn: async () => {
      try {
        if (isMockMode && isMockApi(api)) {
          // Use mock API
          return await api.getForms(status);
        } else if (isRealApi(api)) {
          // Use real API
          const response = await api.get<FormMeta[]>("/forms", {
            params: { status },
          });
          return response.data;
        } else {
          throw new Error("Invalid API configuration");
        }
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error ? error.message : "Failed to fetch forms",
          variant: "destructive",
        });
        throw error;
      }
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Hook for fetching a single form by ID
 */
export function useForm(id: string) {
  const { api, isMockMode } = useApiSwitch();
  const { toast } = useToast();

  return useQuery({
    queryKey: queryKeys.form(id),
    queryFn: async () => {
      try {
        if (isMockMode && isMockApi(api)) {
          // Use mock API
          const form = await api.getFormById(id);
          if (!form) {
            throw new Error(`Form with ID ${id} not found`);
          }
          return form;
        } else if (isRealApi(api)) {
          // Use real API
          const response = await api.get<FormSchema>(`/forms/${id}`);
          return response.data;
        } else {
          throw new Error("Invalid API configuration");
        }
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error ? error.message : "Failed to fetch form",
          variant: "destructive",
        });
        throw error;
      }
    },
    enabled: !!id,
  });
}

/**
 * Hook for creating a new form
 */
export function useCreateForm() {
  const { api, isMockMode } = useApiSwitch();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (formData: Partial<FormSchema>) => {
      try {
        if (isMockMode && isMockApi(api)) {
          // Use mock API
          return await api.createForm(formData);
        } else if (isRealApi(api)) {
          // Use real API
          const response = await api.post<FormSchema>("/forms", formData);
          return response.data;
        } else {
          throw new Error("Invalid API configuration");
        }
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error ? error.message : "Failed to create form",
          variant: "destructive",
        });
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidate forms list queries when a new form is created
      queryClient.invalidateQueries({ queryKey: queryKeys.forms() });

      toast({
        title: "Success",
        description: "Form created successfully",
      });
    },
  });
}

/**
 * Hook for updating an existing form
 */
export function useUpdateForm(id: string) {
  const { api, isMockMode } = useApiSwitch();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (formData: Partial<FormSchema>) => {
      try {
        if (isMockMode && isMockApi(api)) {
          // Use mock API
          return await api.updateForm(id, formData);
        } else if (isRealApi(api)) {
          // Use real API
          const response = await api.put<FormSchema>(`/forms/${id}`, formData);
          return response.data;
        } else {
          throw new Error("Invalid API configuration");
        }
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error ? error.message : "Failed to update form",
          variant: "destructive",
        });
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidate both the forms list and the specific form
      queryClient.invalidateQueries({ queryKey: queryKeys.forms() });
      queryClient.invalidateQueries({ queryKey: queryKeys.form(id) });

      toast({
        title: "Success",
        description: "Form updated successfully",
      });
    },
  });
}

/**
 * Hook for deleting a form
 */
export function useDeleteForm() {
  const { api, isMockMode } = useApiSwitch();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (id: string) => {
      try {
        if (isMockMode && isMockApi(api)) {
          // Use mock API
          await api.deleteForm(id);
        } else if (isRealApi(api)) {
          // Use real API
          await api.delete(`/forms/${id}`);
        } else {
          throw new Error("Invalid API configuration");
        }
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error ? error.message : "Failed to delete form",
          variant: "destructive",
        });
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidate forms list queries when a form is deleted
      queryClient.invalidateQueries({ queryKey: queryKeys.forms() });

      toast({
        title: "Success",
        description: "Form deleted successfully",
      });
    },
  });
}

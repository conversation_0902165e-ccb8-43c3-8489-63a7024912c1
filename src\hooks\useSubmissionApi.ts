import { useApiQuery, useApiMutation, useInvalidateQueries } from "./useApi";
import { queryKeys } from "@/lib/types/api";
import {
  FormSubmission,
  SubmissionMeta,
  SubmissionStatus,
} from "@/lib/types/submission";

/**
 * Hook for fetching all submissions with optional filtering
 */
export function useSubmissions(
  status?: SubmissionStatus,
  applicantId?: string
) {
  const queryKey = [...queryKeys.submissions(), { status, applicantId }];

  return useApiQuery<SubmissionMeta[]>(
    queryKey,
    "/submissions",
    { status, applicantId },
    {
      // Keep submissions data fresh for a shorter time (1 minute)
      staleTime: 1 * 60 * 1000,
    }
  );
}

/**
 * Hook for fetching a single submission by ID
 */
export function useSubmission(id: string) {
  return useApiQuery<FormSubmission>(
    queryKeys.submission(id),
    `/submissions/${id}`,
    undefined,
    {
      // Don't fetch if no ID is provided
      enabled: !!id,
    }
  );
}

/**
 * Hook for creating a new submission
 */
export function useCreateSubmission() {
  const { invalidateQueries } = useInvalidateQueries();

  return useApiMutation<FormSubmission, Partial<FormSubmission>>(
    "/submissions",
    "POST",
    {
      onSuccess: (data) => {
        // Invalidate submissions list queries when a new submission is created
        invalidateQueries(queryKeys.submissions());

        // If the submission has a form ID, invalidate that form's submissions
        if (data.formId) {
          invalidateQueries([...queryKeys.forms(), data.formId, "submissions"]);
        }
      },
      meta: {
        // Custom success message
        successMessage: "Submission created successfully",
      },
    }
  );
}

/**
 * Hook for updating an existing submission
 */
export function useUpdateSubmission(id: string) {
  const { invalidateQueries } = useInvalidateQueries();

  return useApiMutation<FormSubmission, Partial<FormSubmission>>(
    `/submissions/${id}`,
    "PUT",
    {
      onSuccess: (data) => {
        // Invalidate both the submissions list and the specific submission
        invalidateQueries(queryKeys.submissions());
        invalidateQueries(queryKeys.submission(id));

        // If the submission has a form ID, invalidate that form's submissions
        if (data.formId) {
          invalidateQueries([...queryKeys.forms(), data.formId, "submissions"]);
        }
      },
      meta: {
        // Custom success message
        successMessage: "Submission updated successfully",
      },
    }
  );
}

/**
 * Hook for submitting a draft submission
 */
export function useSubmitSubmission(id: string) {
  const { invalidateQueries } = useInvalidateQueries();

  return useApiMutation<FormSubmission, void>(
    `/submissions/${id}/submit`,
    "POST",
    {
      onSuccess: (data) => {
        // Invalidate both the submissions list and the specific submission
        invalidateQueries(queryKeys.submissions());
        invalidateQueries(queryKeys.submission(id));

        // If the submission has a form ID, invalidate that form's submissions
        if (data.formId) {
          invalidateQueries([...queryKeys.forms(), data.formId, "submissions"]);
        }
      },
      meta: {
        // Custom success message
        successMessage: "Submission submitted successfully",
      },
    }
  );
}

/**
 * Hook for deleting a submission
 */
export function useDeleteSubmission() {
  const { invalidateQueries } = useInvalidateQueries();

  return useApiMutation<void, string>(
    `/submissions`, // The ID will be appended in the mutation function
    "DELETE",
    {
      // Override the default mutation function
      onMutate: async (id: string) => {
        const response = await fetch(`/api/submissions/${id}`, {
          method: "DELETE",
        });

        if (!response.ok) {
          throw new Error("Failed to delete submission");
        }
      },
      onSuccess: () => {
        // Invalidate submissions list queries when a submission is deleted
        invalidateQueries(queryKeys.submissions());
      },
      meta: {
        // Custom success message
        successMessage: "Submission deleted successfully",
      },
    }
  );
}

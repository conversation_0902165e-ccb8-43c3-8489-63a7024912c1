import {
  FormSubmission,
  SubmissionMeta,
  SubmissionStatus,
  FormSubmissionCreate,
  FormSubmissionUpdate,
} from "../types/submission";
import { FormService } from "./form-service";
import { AuthService } from "./auth-service";
import { CognitoService } from "./cognito-service";

// Simulate API delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Mock submissions data
const mockSubmissions: FormSubmission[] = [
  {
    id: "1",
    formId: "1",
    formSchema: {} as any, // Will be populated when fetched
    applicantId: "applicant-1",
    applicant: {} as any, // Will be populated when fetched
    status: "submitted",
    data: {
      fullName: "<PERSON> Doe",
      email: "<EMAIL>",
      rating: "5",
      comments: "Great service, very satisfied with the product.",
    },
    createdAt: "2023-07-10T09:15:00Z",
    updatedAt: "2023-07-10T10:30:00Z",
    submittedAt: "2023-07-10T10:30:00Z",
  },
  {
    id: "2",
    formId: "2",
    formSchema: {} as any, // Will be populated when fetched
    applicantId: "applicant-1",
    applicant: {} as any, // Will be populated when fetched
    status: "draft",
    data: {
      fullName: "John Doe",
      email: "<EMAIL>",
      department: "",
      startDate: "",
    },
    createdAt: "2023-07-15T14:20:00Z",
    updatedAt: "2023-07-15T14:45:00Z",
  },
];

/**
 * Service for managing form submissions
 */
export const SubmissionService = {
  /**
   * Get all submissions with optional filtering
   */
  getSubmissions: async (
    status?: SubmissionStatus,
    applicantId?: string
  ): Promise<SubmissionMeta[]> => {
    await delay(500); // Simulate network delay

    // If no applicantId is provided, try to get the current user's ID
    if (!applicantId) {
      try {
        const currentUser = await CognitoService.getCurrentUser();
        if (currentUser && currentUser.role === "applicant") {
          // For applicant users, only show their own submissions
          applicantId = currentUser.id;
        }
      } catch (error) {
        console.error("Error getting current user from Cognito:", error);
        // Continue without filtering by applicantId
      }
    }

    // Filter submissions based on parameters
    let filteredSubmissions = [...mockSubmissions];

    if (status) {
      filteredSubmissions = filteredSubmissions.filter(
        (submission) => submission.status === status
      );
    }

    if (applicantId) {
      filteredSubmissions = filteredSubmissions.filter(
        (submission) => submission.applicantId === applicantId
      );
    }

    // For each submission, fetch the form name and user info
    const submissionMetas: SubmissionMeta[] = await Promise.all(
      filteredSubmissions.map(async (submission) => {
        const form = await FormService.getFormById(submission.formId);

        // Try to get user info from Cognito first, then fall back to AuthService
        let user = null;
        try {
          const currentUser = await CognitoService.getCurrentUser();
          if (currentUser && currentUser.id === submission.applicantId) {
            user = currentUser;
          } else {
            user = await AuthService.getUserById(submission.applicantId);
          }
        } catch (error) {
          console.error("Error getting user from Cognito:", error);
          user = await AuthService.getUserById(submission.applicantId);
        }

        return {
          id: submission.id,
          formId: submission.formId,
          formName: form?.name || "Unknown Form",
          applicantId: submission.applicantId,
          applicantName: user?.name || "Unknown User",
          status: submission.status,
          createdAt: submission.createdAt,
          updatedAt: submission.updatedAt,
          submittedAt: submission.submittedAt,
          reviewedAt: submission.reviewedAt,
          reviewedBy: submission.reviewedBy,
        };
      })
    );

    return submissionMetas;
  },

  /**
   * Get a specific submission by ID
   */
  getSubmissionById: async (id: string): Promise<FormSubmission | null> => {
    await delay(300);

    const submission = mockSubmissions.find((s) => s.id === id);
    if (!submission) {
      return null;
    }

    // Fetch the complete form schema
    const formSchema = await FormService.getFormById(submission.formId);
    if (!formSchema) {
      return null;
    }

    // Try to get the applicant from Cognito first
    let applicant = null;

    try {
      // Check if the current user is the applicant
      const currentUser = await CognitoService.getCurrentUser();
      if (currentUser && currentUser.id === submission.applicantId) {
        applicant = currentUser;
      } else {
        // Fall back to AuthService which will try Cognito first, then mock users
        applicant = await AuthService.getUserById(submission.applicantId);
      }
    } catch (error) {
      console.error("Error getting applicant from Cognito:", error);
      // Fall back to AuthService
      applicant = await AuthService.getUserById(submission.applicantId);
    }

    if (!applicant) {
      return null;
    }

    return {
      ...submission,
      formSchema,
      applicant,
    };
  },

  /**
   * Create a new submission (start a form)
   */
  createSubmission: async (
    submission: FormSubmissionCreate
  ): Promise<FormSubmission> => {
    await delay(700);

    // If no applicant is provided, try to get the current user
    if (!submission.applicant || !submission.applicantId) {
      try {
        const currentUser = await CognitoService.getCurrentUser();
        if (currentUser) {
          submission.applicant = currentUser;
          submission.applicantId = currentUser.id;
        }
      } catch (error) {
        console.error("Error getting current user from Cognito:", error);
        // Continue with the provided applicant or null
      }
    }

    const now = new Date().toISOString();
    const newId = (
      Math.max(...mockSubmissions.map((s) => parseInt(s.id) || 0)) + 1
    ).toString();

    const newSubmission: FormSubmission = {
      ...submission,
      id: newId,
      createdAt: now,
      updatedAt: now,
    };

    // Add to mock data
    mockSubmissions.push(newSubmission);

    return newSubmission;
  },

  /**
   * Update an existing submission (save progress or submit)
   */
  updateSubmission: async (
    id: string,
    updates: FormSubmissionUpdate
  ): Promise<FormSubmission> => {
    await delay(500);

    const submissionIndex = mockSubmissions.findIndex((s) => s.id === id);

    if (submissionIndex === -1) {
      throw new Error(`Submission with ID ${id} not found`);
    }

    const now = new Date().toISOString();
    const existingSubmission = mockSubmissions[submissionIndex];

    // If status is changing to submitted, set submittedAt
    const submittedAt =
      updates.status === "submitted" &&
      existingSubmission.status !== "submitted"
        ? now
        : existingSubmission.submittedAt;

    // If status is changing to approved/rejected, set reviewedAt
    const reviewedAt =
      (updates.status === "approved" || updates.status === "rejected") &&
      existingSubmission.status !== "approved" &&
      existingSubmission.status !== "rejected"
        ? now
        : existingSubmission.reviewedAt;

    const updatedSubmission: FormSubmission = {
      ...existingSubmission,
      ...updates,
      updatedAt: now,
      submittedAt,
      reviewedAt,
    };

    // Update in mock data
    mockSubmissions[submissionIndex] = updatedSubmission;

    return updatedSubmission;
  },

  /**
   * Delete a submission
   */
  deleteSubmission: async (id: string): Promise<boolean> => {
    await delay(400);

    const submissionIndex = mockSubmissions.findIndex((s) => s.id === id);

    if (submissionIndex === -1) {
      return false;
    }

    // Remove from mock data
    mockSubmissions.splice(submissionIndex, 1);

    return true;
  },
};

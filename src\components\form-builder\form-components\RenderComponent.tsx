import { memo } from "react";
import { Controller } from "react-hook-form";
import { FormComponent } from "@/lib/schemas/form-schemas";
import {
  isTextComponent,
  isNumberComponent,
  isDateComponent,
  isDateTimeComponent,
  isSelectComponent,
  isCheckboxComponent,
  isRadioComponent,
  isDataGridComponent,
  isStepComponent,
  isSectionComponent,
  isInfoTextComponent,
} from "@/lib/utils/zod-validation-utils";
import DataGridComponent from "../DataGridComponent";
import StepComponent from "./StepComponent";
import SectionComponent from "./SectionComponent";
import {
  TextInput,
  NumberInput,
  DateInput,
  DateTimeInput,
  SelectInput,
  CheckboxInput,
  CheckboxGroupInput,
  RadioInput,
  InfoTextInput,
} from "./InputComponents";

interface RenderComponentProps {
  readonly component: FormComponent;
  readonly register: any;
  readonly control: any;
  readonly errors: any;
  readonly setValue: any;
  readonly watch: any;
  readonly validationRules: Record<string, any>;
  readonly allComponents?: FormComponent[]; // For rendering nested components
  readonly mode?: "edit" | "preview" | "submission"; // Mode for rendering components
}

/**
 * Renders a form component based on its type
 */
function RenderComponent({
  component,
  register,
  control,
  errors,
  setValue,
  watch,
  validationRules,
  allComponents = [],
  mode = "edit",
}: RenderComponentProps) {
  // Use type guards for better type safety
  if (isStepComponent(component)) {
    // Steps are handled at a higher level in the form structure
    return <StepComponent component={component} />;
  }

  if (isSectionComponent(component)) {
    // Render a section as a collapsible accordion
    return (
      <SectionComponent
        component={component}
        formProps={{
          watch,
          register,
          control,
          errors,
          setValue,
        }}
        allComponents={allComponents}
      />
    );
  }

  if (isTextComponent(component)) {
    return (
      <TextInput
        id={component.id}
        name={component.name}
        placeholder={component.placeholder}
        register={register}
        validationRules={validationRules}
        errors={errors}
      />
    );
  }

  if (isNumberComponent(component)) {
    return (
      <NumberInput
        id={component.id}
        name={component.name}
        placeholder={component.placeholder}
        min={component.min}
        max={component.max}
        step={component.step}
        unit={component.unit}
        register={register}
        validationRules={validationRules}
        errors={errors}
      />
    );
  }

  if (isDateComponent(component)) {
    return (
      <DateInput
        id={component.id}
        name={component.name}
        min={component.min}
        max={component.max}
        register={register}
        validationRules={validationRules}
        errors={errors}
      />
    );
  }

  if (isDateTimeComponent(component)) {
    return (
      <DateTimeInput
        id={component.id}
        name={component.name}
        min={component.min}
        max={component.max}
        register={register}
        validationRules={validationRules}
        errors={errors}
      />
    );
  }

  if (isSelectComponent(component)) {
    return (
      <SelectInput
        id={component.id}
        name={component.name}
        placeholder={component.placeholder}
        options={component.options}
        control={control}
        validationRules={validationRules}
        errors={errors}
        defaultValue={component.defaultValue}
      />
    );
  }

  if (isCheckboxComponent(component)) {
    // If options are provided, use CheckboxGroupInput, otherwise use single CheckboxInput
    if (component.options && component.options.length > 0) {
      return (
        <CheckboxGroupInput
          id={component.id}
          name={component.name}
          options={component.options}
          control={control}
          validationRules={validationRules}
          errors={errors}
        />
      );
    }
    return (
      <CheckboxInput
        id={component.id}
        name={component.name}
        control={control}
        validationRules={validationRules}
        errors={errors}
      />
    );
  }

  if (isRadioComponent(component)) {
    return (
      <RadioInput
        id={component.id}
        name={component.name}
        options={component.options}
        control={control}
        validationRules={validationRules}
        errors={errors}
      />
    );
  }

  if (isDataGridComponent(component)) {
    return (
      <Controller
        name={component.name}
        control={control}
        rules={validationRules}
        render={({ field }) => {
          return (
            <DataGridComponent
              component={component}
              value={field.value ?? {}}
              mode={mode}
              onChange={(value) => {
                // If we receive the new format with both flat and structured data,
                // we only want to store the structured format in the form data
                if (
                  value &&
                  typeof value === "object" &&
                  "structured" in value
                ) {
                  field.onChange(value.structured);
                } else {
                  field.onChange(value);
                }
              }}
            />
          );
        }}
      />
    );
  }

  if (isInfoTextComponent(component)) {
    return (
      <InfoTextInput
        id={component.id}
        infoContent={component.infoContent}
        variant={component.variant}
      />
    );
  }

  // This should never happen if all component types are handled above
  // But we need to handle the case for TypeScript
  return (
    <div>Unsupported component type: {(component as FormComponent).type}</div>
  );
}

export default memo(RenderComponent);

import { DynamicPage } from "@/components/dynamic-page/DynamicPage";
import { AttachToFundingRoundDialog } from "@/components/dialogs/attach-to-funding-round-dialog";
import { AttachFundingRoundProvider } from "@/contexts/attach-funding-round-context";
import { PageConfig } from "@/lib/types/page-config";

interface ProjectListWithDialogProps {
  config: PageConfig;
}

function ProjectListContent({ config }: ProjectListWithDialogProps) {
  return (
    <>
      <DynamicPage config={config} />
      <AttachToFundingRoundDialog />
    </>
  );
}

export function ProjectListWithDialog({ config }: ProjectListWithDialogProps) {
  return (
    <AttachFundingRoundProvider>
      <ProjectListContent config={config} />
    </AttachFundingRoundProvider>
  );
}

import {
  FormComponent,
  FormSchema,
  ConditionalRendering,
  GridRowConditionalRendering,
  DataGridComponent,
  StepComponent,
  SectionComponent,
  TextComponent,
  NumberComponent,
  DateComponent,
  DateTimeComponent,
  SelectComponent,
  CheckboxComponent,
  RadioComponent,
  InfoTextComponent,
} from "@/lib/schemas/form-schemas";

import type {
  FormComponent as OldFormComponent,
  FormSchema as OldFormSchema,
  ConditionalRendering as OldConditionalRendering,
  GridRowConditionalRendering as OldGridRowConditionalRendering,
} from "@/lib/types/form";

/**
 * Converts a conditional rendering object from the old type to the new Zod schema
 */
export function convertConditionalRendering(
  conditionalRendering?: OldConditionalRendering
): ConditionalRendering | undefined {
  if (!conditionalRendering) return undefined;

  return {
    field: conditionalRendering.field,
    operator: conditionalRendering.operator,
    value: conditionalRendering.value ?? null, // Ensure value is not undefined
  };
}

/**
 * Converts a grid row conditional rendering object from the old type to the new Zod schema
 */
export function convertGridRowConditionalRendering(
  conditionalRow?: OldGridRowConditionalRendering
): GridRowConditionalRendering | undefined {
  if (!conditionalRow) return undefined;

  return {
    cellId: conditionalRow.cellId,
    operator: conditionalRow.operator,
    value: conditionalRow.value ?? null, // Ensure value is not undefined
    targetRowIndex: conditionalRow.targetRowIndex,
  };
}

/**
 * Converts an array of grid row conditional rendering objects from the old type to the new Zod schema
 */
export function convertGridRowConditionalRenderingArray(
  conditionalRows?: OldGridRowConditionalRendering[]
): GridRowConditionalRendering[] | undefined {
  if (!conditionalRows) return undefined;

  return conditionalRows.map(
    convertGridRowConditionalRendering
  ) as GridRowConditionalRendering[];
}

/**
 * Converts a form component from the old type to the new Zod schema
 */
export function convertFormComponent(
  component: OldFormComponent
): FormComponent {
  const baseComponent = {
    id: component.id,
    label: component.label,
    name: component.name,
    type: component.type,
    parentId: component.parentId,
    required: component.required,
    validations: component.validations,
    conditionalRendering: convertConditionalRendering(
      component.conditionalRendering
    ),
    defaultValue: component.defaultValue,
    placeholder: component.placeholder,
  };

  switch (component.type) {
    case "text":
      return {
        ...baseComponent,
        type: "text",
        maxLength: component.maxLength,
        minLength: component.minLength,
      } as TextComponent;
    case "number":
      return {
        ...baseComponent,
        type: "number",
        min: component.min,
        max: component.max,
        step: component.step,
        unit: component.unit,
      } as NumberComponent;
    case "date":
      return {
        ...baseComponent,
        type: "date",
        min: component.min,
        max: component.max,
      } as DateComponent;
    case "datetime":
      return {
        ...baseComponent,
        type: "datetime",
        min: component.min,
        max: component.max,
      } as DateTimeComponent;
    case "select":
      return {
        ...baseComponent,
        type: "select",
        options: component.options || [],
        multiple: component.multiple,
      } as SelectComponent;
    case "checkbox":
      return {
        ...baseComponent,
        type: "checkbox",
        options: component.options,
      } as CheckboxComponent;
    case "radio":
      return {
        ...baseComponent,
        type: "radio",
        options: component.options || [],
      } as RadioComponent;
    case "datagrid":
      return {
        ...baseComponent,
        type: "datagrid",
        rows: component.rows || 0,
        columns: component.columns || 0,
        cells: component.cells || {},
        conditionalRows: convertGridRowConditionalRenderingArray(
          component.conditionalRows
        ),
      } as DataGridComponent;
    case "step":
      return {
        ...baseComponent,
        type: "step",
        children: component.children || [],
        icon: component.icon,
        description: component.description,
      } as StepComponent;
    case "section":
      return {
        ...baseComponent,
        type: "section",
        children: component.children || [],
        collapsible: component.collapsible,
        defaultCollapsed: component.defaultCollapsed,
        description: component.description,
      } as SectionComponent;
    case "infoText":
      return {
        ...baseComponent,
        type: "infoText",
        infoContent: component.infoContent ?? "",
        variant: component.variant ?? "default",
      } as InfoTextComponent;
    default:
      return baseComponent as FormComponent;
  }
}

/**
 * Converts an array of form components from the old type to the new Zod schema
 */
export function convertFormComponents(
  components: OldFormComponent[]
): FormComponent[] {
  return components.map(convertFormComponent);
}

/**
 * Converts a form schema from the old type to the new Zod schema
 */
export function convertFormSchema(schema: OldFormSchema): FormSchema {
  return {
    id: schema.id,
    name: schema.name,
    description: schema.description,
    status: schema.status,
    createdAt: schema.createdAt,
    updatedAt: schema.updatedAt,
    components: convertFormComponents(schema.components),
  };
}

import { FormMeta, FormSchema, FormStatus } from "@/lib/types/form";

/**
 * <PERSON><PERSON> forms data for development
 */
export const mockForms: FormMeta[] = [
  {
    id: "form-1",
    name: "Project Application Form",
    description: "Application form for new projects",
    status: "active" as FormStatus,
    createdAt: "2023-01-15T10:30:00Z",
    updatedAt: "2023-02-20T14:45:00Z",
  },
  {
    id: "form-2",
    name: "Funding Request Form",
    description: "Form for requesting project funding",
    status: "active" as FormStatus,
    createdAt: "2023-03-05T09:15:00Z",
    updatedAt: "2023-03-10T11:20:00Z",
  },
  {
    id: "form-3",
    name: "Project Progress Report",
    description: "Form for reporting project progress",
    status: "draft" as FormStatus,
    createdAt: "2023-04-12T16:00:00Z",
    updatedAt: "2023-04-12T16:00:00Z",
  },
];

/**
 * Mock form schemas for development
 */
export const mockFormSchemas: Record<string, FormSchema> = {
  "form-1": {
    id: "form-1",
    name: "Project Application Form",
    description: "Application form for new projects",
    status: "active" as FormStatus,
    components: [
      {
        id: "field-1",
        type: "text",
        name: "projectName",
        label: "Project Name",
        required: true,
        minLength: 3,
        maxLength: 100,
      },
      {
        id: "field-2",
        type: "text",
        name: "projectDescription",
        label: "Project Description",
        required: true,
        minLength: 50,
        maxLength: 1000,
      },
      {
        id: "field-3",
        type: "select",
        name: "projectCategory",
        label: "Project Category",
        required: true,
        options: [
          { label: "Energy", value: "energy" },
          { label: "Transportation", value: "transportation" },
          { label: "Agriculture", value: "agriculture" },
          { label: "Other", value: "other" },
        ],
      },
    ],
    createdAt: "2023-01-15T10:30:00Z",
    updatedAt: "2023-02-20T14:45:00Z",
  },
  "form-2": {
    id: "form-2",
    name: "Funding Request Form",
    description: "Form for requesting project funding",
    status: "active" as FormStatus,
    components: [
      {
        id: "field-1",
        type: "text",
        name: "projectId",
        label: "Project ID",
        required: true,
      },
      {
        id: "field-2",
        type: "number",
        name: "requestedAmount",
        label: "Requested Amount (£)",
        required: true,
        min: 1000,
        max: 1000000,
      },
      {
        id: "field-3",
        type: "text",
        name: "fundingJustification",
        label: "Funding Justification",
        required: true,
        minLength: 100,
        maxLength: 2000,
      },
    ],
    createdAt: "2023-03-05T09:15:00Z",
    updatedAt: "2023-03-10T11:20:00Z",
  },
  "form-3": {
    id: "form-3",
    name: "Project Progress Report",
    description: "Form for reporting project progress",
    status: "draft" as FormStatus,
    components: [
      {
        id: "field-1",
        type: "text",
        name: "projectId",
        label: "Project ID",
        required: true,
      },
      {
        id: "field-2",
        type: "select",
        name: "progressStatus",
        label: "Progress Status",
        required: true,
        options: [
          { label: "On Track", value: "on-track" },
          { label: "Minor Delays", value: "minor-delays" },
          { label: "Major Delays", value: "major-delays" },
          { label: "Completed", value: "completed" },
        ],
      },
      {
        id: "field-3",
        type: "text",
        name: "progressDetails",
        label: "Progress Details",
        required: true,
        minLength: 50,
        maxLength: 1000,
      },
    ],
    createdAt: "2023-04-12T16:00:00Z",
    updatedAt: "2023-04-12T16:00:00Z",
  },
};

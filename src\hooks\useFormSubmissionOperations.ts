import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { UseFormReturn } from "react-hook-form";
import { FormSchema } from "@/lib/schemas/form-schemas";
import { FormSubmission } from "@/lib/types/submission";
import { SubmissionService } from "@/lib/services/submission-service";
import { User } from "@/lib/types/auth";
import { processFormData } from "@/lib/utils/form-data-processor";

interface UseFormSubmissionOperationsProps {
  form: FormSchema | null;
  submission: FormSubmission | null;
  methods: UseFormReturn;
  user: User | null;
  setFormStatus: (status: {
    isSubmitted: boolean;
    isValid: boolean;
    message: string;
  }) => void;
  commitCurrentStepData: () => Promise<boolean>;
  setSubmission: (submission: FormSubmission) => void;
}

interface UseFormSubmissionOperationsReturn {
  isSaving: boolean;
  isSubmitting: boolean;
  saveProgress: () => Promise<boolean>;
  submitForm: () => Promise<boolean>;
}

/**
 * Hook for form submission operations (save and submit)
 */
export function useFormSubmissionOperations({
  form,
  submission,
  methods,
  user,
  setFormStatus,
  commitCurrentStepData,
  setSubmission,
}: UseFormSubmissionOperationsProps): UseFormSubmissionOperationsReturn {
  const navigate = useNavigate();
  const [isSaving, setIsSaving] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  /**
   * Save form progress
   */
  const saveProgress = async (): Promise<boolean> => {
    if (!form || !user) {
      console.error("Cannot save progress: No form or user");
      return false;
    }

    // Prevent multiple simultaneous save operations
    if (isSaving) {
      return false;
    }

    setIsSaving(true);

    try {
      // 1. Commit the current step's data to ensure we have the latest values
      await commitCurrentStepData();

      // 2. Get all form data and process it
      const allFormData = methods.getValues();

      const processedData = processFormData(
        allFormData,
        form?.components || []
      );

      let updatedSubmission;

      // 3. If we don't have a submission yet, create one
      if (!submission) {
        const newSubmission = await SubmissionService.createSubmission({
          formId: form.id,
          formSchema: form,
          applicantId: user.id,
          applicant: user,
          status: "draft",
          data: processedData,
        });

        updatedSubmission = newSubmission;
      } else {
        // 4. Otherwise, update the existing submission
        updatedSubmission = await SubmissionService.updateSubmission(
          submission.id,
          {
            data: processedData,
            updatedAt: new Date().toISOString(),
          }
        );
      }

      // 5. Update local submission state with the updated data
      setSubmission(updatedSubmission);

      // 5. Update UI with success message
      setFormStatus({
        isSubmitted: true,
        isValid: true,
        message: "Progress saved successfully!",
      });

      return true;
    } catch (error: unknown) {
      // Handle error when saving progress
      console.error("Error saving form progress:", error);
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      setFormStatus({
        isSubmitted: true,
        isValid: false,
        message: `Failed to save progress: ${errorMessage}`,
      });

      return false;
    } finally {
      setIsSaving(false);
    }
  };

  /**
   * Submit form
   */
  const submitForm = async (): Promise<boolean> => {
    if (!form || !user) {
      console.error("Cannot submit form: No form or user");
      return false;
    }

    // Prevent multiple simultaneous submissions
    if (isSubmitting) {
      return false;
    }

    setIsSubmitting(true);

    try {
      // 1. Commit the current step's data to ensure we have the latest values
      await commitCurrentStepData();

      // 2. Validate the entire form
      const isValid = await methods.trigger();

      if (!isValid) {
        setFormStatus({
          isSubmitted: true,
          isValid: false,
          message: "Please fix the validation errors before submitting.",
        });
        return false;
      }

      // 3. Show submitting status
      setFormStatus({
        isSubmitted: true,
        isValid: true,
        message: "Submitting form...",
      });

      // 4. Get and process all form data
      const allFormData = methods.getValues();

      const processedData = processFormData(
        allFormData,
        form?.components || []
      );

      let updatedSubmission;

      // 5. If we don't have a submission yet, create one and submit it
      if (!submission) {
        const newSubmission = await SubmissionService.createSubmission({
          formId: form.id,
          formSchema: form,
          applicantId: user.id,
          applicant: user,
          status: "submitted", // Create it as submitted directly
          data: processedData,
          submittedAt: new Date().toISOString(),
        });

        updatedSubmission = newSubmission;
      } else {
        // 6. Otherwise, update the existing submission to submitted status
        updatedSubmission = await SubmissionService.updateSubmission(
          submission.id,
          {
            status: "submitted",
            data: processedData,
            updatedAt: new Date().toISOString(),
            submittedAt: new Date().toISOString(),
          }
        );
      }

      // 7. Update local submission state
      setSubmission(updatedSubmission);

      // 7. Update UI with success message
      setFormStatus({
        isSubmitted: true,
        isValid: true,
        message: "Form submitted successfully!",
      });

      // 8. Navigate to applications page after a short delay
      setTimeout(() => {
        navigate("/applications");
      }, 2000);

      return true;
    } catch (error) {
      // Handle error when submitting form
      console.error("Error submitting form:", error);
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      setFormStatus({
        isSubmitted: true,
        isValid: false,
        message: `Failed to submit form: ${errorMessage}`,
      });

      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    isSaving,
    isSubmitting,
    saveProgress,
    submitForm,
  };
}

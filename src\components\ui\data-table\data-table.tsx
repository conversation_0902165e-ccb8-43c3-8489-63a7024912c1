import React, { memo, useMemo } from "react";
import {
  ColumnDef,
  SortingState,
  useReactTable,
  PaginationState,
  VisibilityState,
  getCoreRowModel,
} from "@tanstack/react-table";
import { Table } from "@/components/ui/table";
import { TableFilter } from "@/lib/services/table-service";

// Import custom hooks
import { useColumnPinning } from "./hooks/use-column-pinning";
import { useColumnWidth } from "./hooks/use-column-width";
import { useTableFilter } from "./hooks/use-table-filter";
import { useTableScroll } from "./hooks/use-table-scroll";

// Import components
import { DataTableGlobalFilter } from "./components/data-table-global-filter";
import { DataTableHeader } from "./components/data-table-header";
import { DataTableBody } from "./components/data-table-body";
import { DataTablePagination } from "./components/data-table-pagination";

// Import styles
import { dataTableStyles } from "./styles/data-table-styles";

// Extend the ColumnDef type to include enablePinning and width
declare module "@tanstack/react-table" {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  interface ColumnMeta<TData, TValue> {
    enablePinning?: boolean;
    width?: number; // Column width in pixels
  }
}

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  pageCount: number;
  totalCount: number;
  onPaginationChange: (pagination: PaginationState) => void;
  onSortingChange: (sorting: SortingState) => void;
  onFilterChange: (filter: TableFilter) => void;
  pagination: PaginationState;
  sorting: SortingState;
  isLoading?: boolean;
  enableColumnFilters?: boolean;
  enableGlobalFilter?: boolean;
  enablePinning?: boolean;
  showPinningControls?: boolean;
  defaultPinnedColumns?: Record<string, number>;
  columnWidths?: Record<string, number>;
}

/**
 * DataTable component with support for sorting, filtering, and column pinning
 */
export const DataTable = memo(function DataTable<TData, TValue>({
  columns,
  data,
  pageCount,
  totalCount,
  onPaginationChange,
  onSortingChange,
  onFilterChange,
  pagination,
  sorting,
  isLoading = false,
  enableColumnFilters = true,
  enableGlobalFilter = true,
  enablePinning = true,
  showPinningControls = true,
  defaultPinnedColumns = {},
  columnWidths = {},
}: Readonly<DataTableProps<TData, TValue>>) {
  // State for column visibility
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});

  // Use custom hooks
  const { columnFilters, setColumnFilters, globalFilter, setGlobalFilter } =
    useTableFilter({ onFilterChange });

  const { pinnedColumns, canColumnBePinned, toggleColumnPin, handlePinColumn } =
    useColumnPinning({ columns, enablePinning, defaultPinnedColumns });

  const { getGeneralColumnStyle, getColumnStyle } = useColumnWidth({
    columns,
    columnWidths,
  });

  const { tableContainerRef } = useTableScroll({ pinnedColumns });

  // Create table options object with memoization
  const tableOptions = useMemo(
    () => ({
      data,
      columns,
      pageCount,
      state: {
        sorting,
        pagination,
        columnFilters,
        globalFilter,
        columnVisibility,
      },
      manualPagination: true,
      manualSorting: true,
      manualFiltering: true,
      getCoreRowModel: getCoreRowModel(),
      onSortingChange: (updater: any) => {
        // Handle both function updaters and direct value assignments
        if (typeof updater === "function") {
          const newSorting = updater(sorting);
          onSortingChange(newSorting);
        } else {
          onSortingChange(updater);
        }
      },
      onPaginationChange: (updater: any) => {
        // Handle both function updaters and direct value assignments
        if (typeof updater === "function") {
          const newPagination = updater(pagination);
          onPaginationChange(newPagination);
        } else {
          onPaginationChange(updater);
        }
      },
      onColumnFiltersChange: setColumnFilters,
      onGlobalFilterChange: setGlobalFilter,
      onColumnVisibilityChange: setColumnVisibility,
      // Add custom meta data to the table instance
      meta: {
        toggleColumnPin, // Expose the toggleColumnPin function
        pinnedColumns, // Expose the current pinned columns state
      },
    }),
    [
      data,
      columns,
      pageCount,
      sorting,
      pagination,
      columnFilters,
      globalFilter,
      columnVisibility,
      onSortingChange,
      onPaginationChange,
      setColumnFilters,
      setGlobalFilter,
      toggleColumnPin,
      pinnedColumns,
    ]
  );

  // Initialize the table at the component level
  const table = useReactTable(tableOptions);

  // Helper function to get column style for a specific column
  const getColumnStyleForId = useMemo(() => {
    return (columnId: string) => {
      return getColumnStyle(columnId, pinnedColumns, canColumnBePinned);
    };
  }, [getColumnStyle, pinnedColumns, canColumnBePinned]);

  return (
    <div className="space-y-4">
      <style>{dataTableStyles}</style>

      {/* Global Search */}
      {enableGlobalFilter && (
        <DataTableGlobalFilter
          value={globalFilter}
          onChange={setGlobalFilter}
        />
      )}

      {/* Table */}
      <div className="rounded-md border relative">
        <div
          ref={tableContainerRef}
          className="overflow-x-auto table-container"
          style={{
            position: "relative",
            isolation: "isolate",
            contain: "paint",
            transform: "translateZ(0)",
            willChange: "transform",
          }}
        >
          <Table style={{ position: "relative" }}>
            <DataTableHeader
              table={table}
              enableColumnFilters={enableColumnFilters}
              showPinningControls={showPinningControls}
              pinnedColumns={pinnedColumns}
              canColumnBePinned={canColumnBePinned}
              handlePinColumn={handlePinColumn}
              getGeneralColumnStyle={getGeneralColumnStyle}
              getColumnStyle={getColumnStyleForId}
            />
            <DataTableBody
              table={table}
              columns={columns}
              isLoading={isLoading}
              pinnedColumns={pinnedColumns}
              getGeneralColumnStyle={getGeneralColumnStyle}
              getColumnStyle={getColumnStyleForId}
            />
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <DataTablePagination table={table} totalCount={totalCount} />
    </div>
  );
}) as any;

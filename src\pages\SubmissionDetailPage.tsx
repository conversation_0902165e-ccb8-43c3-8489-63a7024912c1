import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { SubmissionService } from "@/lib/services/submission-service";
import { FormSubmission, SubmissionStatus } from "@/lib/types/submission";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { CustomBadge } from "@/components/ui/custom-badge";
import { Textarea } from "@/components/ui/textarea";
import { Loading } from "@/components/ui/loading";
import {
  <PERSON>L<PERSON><PERSON>,
  Calendar,
  CheckCircle,
  Edit,
  User,
  XCircle,
  CornerDownLeft,
} from "lucide-react";
// Import our new submission data viewer component
import { SubmissionDataViewer } from "@/components/submission/SubmissionDataViewer";

// Format date for display
const formatDate = (dateString?: string) => {
  if (!dateString) return "N/A";
  const date = new Date(dateString);
  return date.toLocaleDateString("en-GB", {
    day: "numeric",
    month: "short",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// Get badge variant based on submission status
const getStatusBadgeVariant = (status: SubmissionStatus) => {
  switch (status) {
    case "draft":
      return "outline";
    case "submitted":
      return "default";
    case "approved":
      return "success";
    case "rejected":
      return "destructive";
    case "returned":
      return "warning";
    default:
      return "outline";
  }
};

export default function SubmissionDetailPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user, hasPermission } = useAuth();
  const { toast } = useToast();
  const isAdmin = hasPermission("admin");

  const [submission, setSubmission] = useState<FormSubmission | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [reviewNotes, setReviewNotes] = useState("");
  const [isUpdating, setIsUpdating] = useState(false);

  // Load submission data
  useEffect(() => {
    const loadSubmission = async () => {
      if (!id) return;

      setIsLoading(true);
      try {
        const data = await SubmissionService.getSubmissionById(id);
        if (!data) {
          navigate("/submissions");
          return;
        }

        setSubmission(data);
        setReviewNotes(data.reviewNotes ?? "");
      } catch (error) {
        console.error("Failed to load submission:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSubmission();
  }, [id, navigate]);

  // Check if current user has access to this submission
  const hasAccess = () => {
    if (!user || !submission) return false;

    // Admins can access all submissions
    if (user.role === "admin") return true;

    // Applicants can only access their own submissions
    return user.id === submission.applicantId;
  };

  // Update submission status
  const updateStatus = async (status: SubmissionStatus) => {
    if (!submission || !id) return;

    setIsUpdating(true);
    try {
      const updatedSubmission = await SubmissionService.updateSubmission(id, {
        status,
        reviewNotes,
        reviewedBy: user?.id,
      });

      setSubmission(updatedSubmission);

      // Determine toast variant based on status
      let toastVariant: "default" | "destructive" | "success" = "default";
      if (status === "rejected") {
        toastVariant = "destructive";
      } else if (status === "approved") {
        toastVariant = "success";
      }

      // Show toast notification
      toast({
        title: `Submission ${status}`,
        description: `The submission has been ${status} successfully.`,
        variant: toastVariant,
      });
    } catch (error) {
      console.error(`Failed to ${status} submission:`, error);

      // Show error toast
      toast({
        title: "Action failed",
        description: `Failed to ${status} submission. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  if (isLoading) {
    return <Loading message="Loading submission..." />;
  }

  if (!submission || !hasAccess()) {
    return (
      <div className="flex flex-col items-center justify-center py-10 text-center">
        <p className="text-muted-foreground">
          Submission not found or you don't have permission to access it.
        </p>
        <Button asChild className="mt-4">
          <Link to="/submissions">Back to Submissions</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild className="h-8 w-8">
            <Link to="/submissions">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">
            Submission Details
          </h1>
        </div>
        <CustomBadge
          variant={getStatusBadgeVariant(submission.status)}
          className="text-sm"
        >
          {submission.status.charAt(0).toUpperCase() +
            submission.status.slice(1)}
        </CustomBadge>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{submission.formSchema.name}</CardTitle>
          <CardDescription>{submission.formSchema.description}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <div className="flex items-center text-sm">
                <User className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>Applicant: {submission.applicant.name}</span>
              </div>
              <div className="flex items-center text-sm">
                <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>Created: {formatDate(submission.createdAt)}</span>
              </div>
              <div className="flex items-center text-sm">
                <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                <span>Updated: {formatDate(submission.updatedAt)}</span>
              </div>
            </div>
            <div className="space-y-2">
              {submission.submittedAt && (
                <div className="flex items-center text-sm">
                  <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>Submitted: {formatDate(submission.submittedAt)}</span>
                </div>
              )}
              {submission.reviewedAt && (
                <div className="flex items-center text-sm">
                  <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                  <span>Reviewed: {formatDate(submission.reviewedAt)}</span>
                </div>
              )}
            </div>
          </div>

          <Tabs defaultValue="submission" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="submission">Submission Data</TabsTrigger>
              <TabsTrigger value="review">Review</TabsTrigger>
            </TabsList>
            <TabsContent value="submission" className="space-y-4">
              {/* Use our new SubmissionDataViewer component */}
              <div className="py-2">
                <SubmissionDataViewer submission={submission} />
              </div>

              {/* Keep the raw JSON view as an option for debugging */}
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="raw-json">
                  <AccordionTrigger>View Raw JSON Data</AccordionTrigger>
                  <AccordionContent>
                    <pre className="rounded-md bg-muted p-4 overflow-auto">
                      {JSON.stringify(submission.data, null, 2)}
                    </pre>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </TabsContent>
            <TabsContent value="review" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium">Review Notes</h3>
                  <p className="text-sm text-muted-foreground">
                    Add notes about this submission for the applicant or other
                    admins.
                  </p>
                </div>
                <Textarea
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  placeholder="Enter review notes here..."
                  className="min-h-[150px]"
                  disabled={!isAdmin || isUpdating}
                />
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-between">
          {submission.status === "draft" &&
            user?.id === submission.applicantId && (
              <Button asChild>
                <Link
                  to={`/applications/${submission.formId}/submissions/${submission.id}`}
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Continue Editing
                </Link>
              </Button>
            )}

          {isAdmin && submission.status === "submitted" && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => updateStatus("returned")}
                disabled={isUpdating}
              >
                <CornerDownLeft className="mr-2 h-4 w-4" />
                Return for Changes
              </Button>
              <Button
                variant="destructive"
                onClick={() => updateStatus("rejected")}
                disabled={isUpdating}
              >
                <XCircle className="mr-2 h-4 w-4" />
                Reject
              </Button>
              <Button
                variant="default"
                onClick={() => updateStatus("approved")}
                disabled={isUpdating}
              >
                <CheckCircle className="mr-2 h-4 w-4" />
                Approve
              </Button>
            </div>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}

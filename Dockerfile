# syntax=docker/dockerfile:1

# Multi-stage build for React + TypeScript + Vite application

# Build stage
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files first for better layer caching
COPY package*.json ./

# Install dependencies with cache mount for better performance
RUN --mount=type=cache,target=/root/.npm \
  npm ci --only=production=false

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine AS production

# Install wget for health checks (if not already present)
RUN apk add --no-cache wget

# Create non-root user for better security
RUN addgroup -g 1001 -S nodejs && \
  adduser -S nextjs -u 1001

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built application from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy environment template for runtime environment variable injection
COPY public/env.js /usr/share/nginx/html/env.js
COPY docker-entrypoint.sh /docker-entrypoint.sh

# Make entrypoint script executable and set proper ownership
RUN chmod +x /docker-entrypoint.sh && \
  chown -R nextjs:nodejs /usr/share/nginx/html && \
  chown nextjs:nodejs /docker-entrypoint.sh

# Expose port 80
EXPOSE 80

# Health check with improved reliability
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:80/health || exit 1

# Use custom entrypoint for environment variable injection
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]

# Development stage
FROM node:20-alpine AS development

# Set working directory
WORKDIR /app

# Copy package files first for better layer caching
COPY package*.json ./

# Install all dependencies (including dev dependencies) with cache mount
RUN --mount=type=cache,target=/root/.npm \
  npm ci

# Copy source code
COPY . .

# Copy and make development environment injection script executable
COPY scripts/inject-dev-env.sh /inject-dev-env.sh

# Create non-root user for development and set permissions
RUN chmod +x /inject-dev-env.sh && \
  addgroup -g 1001 -S nodejs && \
  adduser -S nextjs -u 1001 && \
  chown -R nextjs:nodejs /app && \
  chown nextjs:nodejs /inject-dev-env.sh

# Switch to non-root user
USER nextjs

# Expose development port
EXPOSE 3000

# Use the environment injection script as entrypoint
ENTRYPOINT ["/inject-dev-env.sh"]
CMD ["npm", "run", "dev"]

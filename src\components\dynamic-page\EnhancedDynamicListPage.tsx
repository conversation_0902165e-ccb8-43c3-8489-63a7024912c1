import React, { useMemo, useCallback, useEffect } from "react";
import { ColumnDef, Row } from "@tanstack/react-table";
import { DataTable } from "@/components/ui/data-table/data-table";
import { ListPageConfig, convertToColumnDefs } from "@/lib/types/page-config";
import { useDynamicPage } from "@/hooks/useDynamicPage";
import DynamicPageHeader from "./DynamicPageHeader";
import { useAuth } from "@/contexts/AuthContext";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import {
  DateCellRenderer,
  StatusCellRenderer,
  ProgressCellRenderer,
  ValueCellRenderer,
  ActionsCellRenderer,
} from "./cell-renderers";

interface EnhancedDynamicListPageProps {
  config: ListPageConfig;
  onCreateNew?: () => void;
  onView?: (id: string) => void;
  onEdit?: (id: string) => void;
  refetch?: boolean;
  onRefetchComplete?: () => void;
}

/**
 * Enhanced component for rendering a dynamic list page with improved UI components
 */
export const EnhancedDynamicListPage = React.memo(
  function EnhancedDynamicListPage({
    config,
    onCreateNew,
    onView,
    onEdit,
    refetch,
    onRefetchComplete,
  }: EnhancedDynamicListPageProps) {
    const { hasPermission } = useAuth();
    const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false);
    const [itemToDelete, setItemToDelete] = React.useState<string | null>(null);



    // Use dynamic page hook
    const {
      tableData: rawTableData,
      pageCount: rawPageCount,
      totalCount: rawTotalCount,
      isLoading,
      pagination: rawPagination,
      sorting: rawSorting,
      handlePaginationChange: rawHandlePaginationChange,
      handleSortingChange: rawHandleSortingChange,
      handleFilterChange: rawHandleFilterChange,
      handleCreate,
      refreshData,
      handleView,
      handleEdit,
      handleDelete,
      hasRequiredPermissions,
    } = useDynamicPage({
      config,
    });
    useEffect(() => {
      if (refetch && refreshData) {
        console.log("Refetching data after operation...");
        refreshData();
        // Reset refetch state after calling refreshData
        if (onRefetchComplete) {
          onRefetchComplete();
        }
      }
    }, [refetch, refreshData, onRefetchComplete]);

    // Memoize values to prevent unnecessary re-renders
    const tableData = useMemo(() => rawTableData ?? [], [rawTableData]);
    const pageCount = useMemo(() => rawPageCount ?? 0, [rawPageCount]);
    const totalCount = useMemo(() => rawTotalCount ?? 0, [rawTotalCount]);
    const pagination = useMemo(
      () => rawPagination ?? { pageIndex: 0, pageSize: 10 },
      [rawPagination]
    );
    const sorting = useMemo(() => rawSorting ?? [], [rawSorting]);

    // Memoize handlers to prevent unnecessary re-renders
    const handlePaginationChange = useCallback(
      (newPagination: any) => {
        if (rawHandlePaginationChange) {
          rawHandlePaginationChange(newPagination);
        }
      },
      [rawHandlePaginationChange]
    );

    const handleSortingChange = useCallback(
      (newSorting: any) => {
        if (rawHandleSortingChange) {
          rawHandleSortingChange(newSorting);
        }
      },
      [rawHandleSortingChange]
    );

    const handleFilterChange = useCallback(
      (newFilter: any) => {
        if (rawHandleFilterChange) {
          rawHandleFilterChange(newFilter);
        }
      },
      [rawHandleFilterChange]
    );

    // Define cell renderers outside of the parent component
    const renderDateCell = useCallback(
      (props: {
        row: Row<unknown>;
        column: ColumnDef<unknown, unknown>;
        configColumn: any;
      }) => (
        <DateCellRenderer
          row={props.row}
          columnId={props.column.id as string}
          configColumn={props.configColumn}
          showTime={false}
        />
      ),
      []
    );

    const renderDateTimeCell = useCallback(
      (props: {
        row: Row<unknown>;
        column: ColumnDef<unknown, unknown>;
        configColumn: any;
      }) => (
        <DateCellRenderer
          row={props.row}
          columnId={props.column.id as string}
          configColumn={props.configColumn}
          showTime={props.configColumn.formatOptions?.showTime ?? true}
        />
      ),
      []
    );

    const renderStatusCell = useCallback(
      (props: {
        row: Row<unknown>;
        column: ColumnDef<unknown, unknown>;
        configColumn: any;
      }) => (
        <StatusCellRenderer
          row={props.row}
          columnId={props.column.id as string}
          configColumn={props.configColumn}
        />
      ),
      []
    );

    const renderProgressCell = useCallback(
      (props: {
        row: Row<unknown>;
        column: ColumnDef<unknown, unknown>;
        configColumn: any;
      }) => (
        <ProgressCellRenderer
          row={props.row}
          columnId={props.column.id as string}
          configColumn={props.configColumn}
        />
      ),
      []
    );

    const renderNumberCell = useCallback(
      (props: {
        row: Row<unknown>;
        column: ColumnDef<unknown, unknown>;
        configColumn: any;
      }) => (
        <ValueCellRenderer
          row={props.row}
          columnId={props.column.id as string}
          configColumn={props.configColumn}
          type="number"
        />
      ),
      []
    );

    const renderCurrencyCell = useCallback(
      (props: {
        row: Row<unknown>;
        column: ColumnDef<unknown, unknown>;
        configColumn: any;
      }) => (
        <ValueCellRenderer
          row={props.row}
          columnId={props.column.id as string}
          configColumn={props.configColumn}
          type="currency"
        />
      ),
      []
    );

    const renderPercentCell = useCallback(
      (props: {
        row: Row<unknown>;
        column: ColumnDef<unknown, unknown>;
        configColumn: any;
      }) => (
        <ValueCellRenderer
          row={props.row}
          columnId={props.column.id as string}
          configColumn={props.configColumn}
          type="percent"
          showTrend={true}
        />
      ),
      []
    );

    const renderActionsCell = useCallback(
      (props: { row: Row<unknown> }) => (
        <ActionsCellRenderer
          row={props.row}
          config={config}
          hasPermission={hasPermission}
          onView={onView}
          onEdit={onEdit}
          handleView={handleView}
          handleEdit={handleEdit}
          onDeleteClick={(id) => {
            setItemToDelete(id);
            setDeleteDialogOpen(true);
          }}
        />
      ),
      [
        config,
        hasPermission,
        onView,
        onEdit,
        handleView,
        handleEdit,
        setItemToDelete,
        setDeleteDialogOpen,
      ]
    );

    const columns = useMemo<ColumnDef<unknown, unknown>[]>(() => {
      const configColumns = convertToColumnDefs(config.columns);

      const columnsWithRenderers = configColumns.map((column) => {
        const configColumn = config.columns.find((c) => c.id === column.id);

        if (!configColumn) return column;

        // Add cell renderer based on column type
        switch (configColumn.type) {
          case "date":
            return {
              ...column,
              cell: (info: any) =>
                renderDateCell({ row: info.row, column, configColumn }),
            };
          case "datetime":
            return {
              ...column,
              cell: (info: any) =>
                renderDateTimeCell({ row: info.row, column, configColumn }),
            };
          case "status":
            return {
              ...column,
              cell: (info: any) =>
                renderStatusCell({ row: info.row, column, configColumn }),
            };
          case "progress":
            return {
              ...column,
              cell: (info: any) =>
                renderProgressCell({ row: info.row, column, configColumn }),
            };
          case "number":
            return {
              ...column,
              cell: (info: any) =>
                renderNumberCell({ row: info.row, column, configColumn }),
            };
          case "currency":
            return {
              ...column,
              cell: (info: any) =>
                renderCurrencyCell({ row: info.row, column, configColumn }),
            };
          case "percent":
            return {
              ...column,
              cell: (info: any) =>
                renderPercentCell({ row: info.row, column, configColumn }),
            };
          default:
            return column;
        }
      });

      // Add actions column if there are actions defined
      if (config.actions && config.actions.length > 0) {
        return [
          ...columnsWithRenderers,
          {
            id: "actions",
            header: "Actions",
            enableSorting: false,
            enableColumnFilter: false,
            cell: (info: any) => renderActionsCell({ row: info.row }),
          },
        ];
      }

      return columnsWithRenderers;
    }, [
      config.columns,
      config.actions,
      renderDateCell,
      renderDateTimeCell,
      renderStatusCell,
      renderProgressCell,
      renderNumberCell,
      renderCurrencyCell,
      renderPercentCell,
      renderActionsCell,
    ]);

    // Handle create button click - memoized to prevent unnecessary re-renders
    const handleCreateClick = useCallback(() => {
      if (onCreateNew) {
        onCreateNew();
      } else {
        handleCreate();
      }
    }, [onCreateNew, handleCreate]);

    // Handle delete confirmation - memoized to prevent unnecessary re-renders
    const handleDeleteConfirm = useCallback(async () => {
      if (itemToDelete) {
        await handleDelete(itemToDelete);
        setItemToDelete(null);
      }
      setDeleteDialogOpen(false);
    }, [itemToDelete, handleDelete, setItemToDelete, setDeleteDialogOpen]);

    if (!hasRequiredPermissions) {
      return (
        <div className="flex flex-col items-center justify-center p-8">
          <h2 className="text-2xl font-bold mb-4">Access Denied</h2>
          <p className="text-muted-foreground mb-6">
            You don't have permission to access this page.
          </p>
          <Badge variant="outline">Insufficient Permissions</Badge>
        </div>
      );
    }
    return (
      <div className="space-y-6" data-refresh-trigger>
        <DynamicPageHeader config={config} onCreateNew={handleCreateClick} />

        <DataTable
          columns={columns}
          data={tableData}
          pageCount={pageCount}
          totalCount={totalCount}
          pagination={pagination}
          sorting={sorting}
          onPaginationChange={handlePaginationChange}
          onSortingChange={handleSortingChange}
          onFilterChange={handleFilterChange}
          isLoading={isLoading}
          enableColumnFilters={config.enableColumnFilters ?? true}
          enableGlobalFilter={config.enableGlobalFilter ?? true}
          enablePinning={config.enablePinning ?? true}
          showPinningControls={true}
          defaultPinnedColumns={config.defaultPinnedColumns ?? {}}
        />

        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the
                {config.entityName} and remove the data from our servers.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDeleteConfirm}>
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    );
  }
);

export default EnhancedDynamicListPage;

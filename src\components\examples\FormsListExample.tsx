import { useForms } from "@/hooks/useFormsWithMock";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { formatDate } from "@/lib/utils/date-utils";
import { FormStatus, FormMeta } from "@/lib/types/form";
import { useNavigate } from "react-router-dom";

/**
 * Example component demonstrating the usage of React Query hooks
 * This component fetches and displays a list of forms
 */
export function FormsListExample() {
  const navigate = useNavigate();
  const { data: forms, isLoading, error, refetch } = useForms();

  // Function to get badge color based on form status
  const getStatusBadge = (status: FormStatus) => {
    switch (status) {
      case "draft":
        return <Badge variant="outline">Draft</Badge>;
      case "active":
        return <Badge variant="default">Active</Badge>;
      case "archived" as FormStatus:
        return <Badge variant="secondary">Archived</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Handle view form click
  const handleViewForm = (id: string) => {
    navigate(`/forms/${id}`);
  };

  // If loading, show skeleton UI
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold">Forms</h2>
          <Button variant="outline" disabled>
            Refresh
          </Button>
        </div>
        {[1, 2, 3].map((i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <Skeleton className="h-6 w-1/3" />
              <Skeleton className="h-4 w-2/3" />
            </CardHeader>
            <CardContent>
              <div className="flex justify-between">
                <Skeleton className="h-4 w-1/4" />
                <Skeleton className="h-4 w-1/4" />
              </div>
            </CardContent>
            <CardFooter>
              <Skeleton className="h-9 w-20" />
            </CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  // If error, show error message
  if (error) {
    return (
      <Card className="border-destructive">
        <CardHeader>
          <CardTitle>Error Loading Forms</CardTitle>
          <CardDescription>
            {error instanceof Error
              ? error.message
              : "An unknown error occurred"}
          </CardDescription>
        </CardHeader>
        <CardFooter>
          <Button onClick={() => refetch()}>Retry</Button>
        </CardFooter>
      </Card>
    );
  }

  // Render forms list
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Forms</h2>
        <Button variant="outline" onClick={() => refetch()}>
          Refresh
        </Button>
      </div>

      {forms && forms.length > 0 ? (
        forms.map((form: FormMeta) => (
          <Card key={form.id}>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle>{form.name}</CardTitle>
                {getStatusBadge(form.status)}
              </div>
              <CardDescription>{form.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>Created: {formatDate(form.createdAt)}</span>
                <span>Updated: {formatDate(form.updatedAt)}</span>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" onClick={() => handleViewForm(form.id)}>
                View Form
              </Button>
            </CardFooter>
          </Card>
        ))
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>No Forms Found</CardTitle>
            <CardDescription>
              There are no forms available. Create a new form to get started.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={() => navigate("/forms/new")}>
              Create New Form
            </Button>
          </CardFooter>
        </Card>
      )}
    </div>
  );
}

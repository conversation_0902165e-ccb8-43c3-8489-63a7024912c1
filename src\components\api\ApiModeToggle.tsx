import { useApiSwitch } from "@/hooks/useApiSwitch";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Database, Server } from "lucide-react";

interface ApiModeToggleProps {
  variant?: "switch" | "dropdown" | "badge";
  className?: string;
}

/**
 * Component for toggling between mock and real API modes
 */
export function ApiModeToggle({
  variant = "switch",
  className,
}: Readonly<ApiModeToggleProps>) {
  const { toggleApiMode, isMockMode } = useApiSwitch();

  // Switch variant
  if (variant === "switch") {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <Switch
          id="api-mode"
          checked={!isMockMode}
          onCheckedChange={toggleApiMode}
        />
        <Label htmlFor="api-mode" className="cursor-pointer">
          API Mode: {isMockMode ? "Mock" : "Real"}
        </Label>
      </div>
    );
  }

  // Badge variant
  if (variant === "badge") {
    return (
      <Badge
        variant={isMockMode ? "outline" : "default"}
        className={`cursor-pointer ${className}`}
        onClick={toggleApiMode}
      >
        {isMockMode ? (
          <Database className="mr-1 h-3 w-3" />
        ) : (
          <Server className="mr-1 h-3 w-3" />
        )}
        {isMockMode ? "Mock API" : "Real API"}
      </Badge>
    );
  }

  // Dropdown variant (default)
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className={className}>
          {isMockMode ? (
            <Database className="mr-2 h-4 w-4" />
          ) : (
            <Server className="mr-2 h-4 w-4" />
          )}
          {isMockMode ? "Mock API" : "Real API"}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => isMockMode || toggleApiMode()}>
          <Database className="mr-2 h-4 w-4" />
          <span>Mock API</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => !isMockMode || toggleApiMode()}>
          <Server className="mr-2 h-4 w-4" />
          <span>Real API</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

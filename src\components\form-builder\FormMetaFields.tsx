import { FormSchema } from "@/lib/types/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

interface FormMetaFieldsProps {
  form: FormSchema;
  onFormChange: (field: keyof FormSchema, value: any) => void;
}

export default function FormMetaFields({
  form,
  onFormChange,
}: FormMetaFieldsProps) {
  return (
    <div className="grid gap-6 sm:grid-cols-2">
      <div className="grid gap-2">
        <Label htmlFor="name">Form Name</Label>
        <Input
          id="name"
          value={form.name}
          onChange={(e) => onFormChange("name", e.target.value)}
          placeholder="Enter form name"
          className="w-full"
        />
      </div>
      <div className="grid gap-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={form.description ?? ""}
          onChange={(e) => onFormChange("description", e.target.value)}
          placeholder="Enter form description"
          className="h-[38px] min-h-[38px] w-full resize-none"
        />
      </div>
    </div>
  );
}

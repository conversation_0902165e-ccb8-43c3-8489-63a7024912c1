import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  PageConfig,
  ListPageConfig,
  FormPageConfig,
} from "@/lib/types/page-config";
import EnhancedDynamicListPage from "./EnhancedDynamicListPage";
import DynamicFormPage from "./DynamicFormPage";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { useAuth } from "@/contexts/AuthContext";
import { Badge } from "@/components/ui/badge";
import EditEntityDialog from "./EditEntityDialog";

interface DynamicPageProps {
  config: PageConfig;
  entityId?: string;
}

export const DynamicPage = React.memo(function DynamicPage({
  config,
  entityId,
}: DynamicPageProps) {
  const { hasPermission } = useAuth();
  const navigate = useNavigate();
  const [showForm, setShowForm] = useState(
    !!entityId || config.type === "form"
  );
  const [refetch, setRefetch] = useState(false);
  const [currentEntityId, setCurrentEntityId] = useState<string | null>(
    entityId ?? null
  );

  const hasRequiredPermissions =
    !config.permissions ||
    config.permissions.every((permission) => hasPermission(permission as any));

  if (!hasRequiredPermissions) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <h2 className="text-2xl font-bold mb-4">Access Denied</h2>
        <p className="text-muted-foreground mb-6">
          You don't have permission to access this page.
        </p>
        <Badge variant="outline">Insufficient Permissions</Badge>
      </div>
    );
  }

  if (config.type === "form") {
    return (
      <DynamicFormPage
        config={config as FormPageConfig}
        entityId={currentEntityId ?? undefined}
      />
    );
  }

  return (
    <>
      <EnhancedDynamicListPage
        config={config as ListPageConfig}
        refetch={refetch}
        onRefetchComplete={() => setRefetch(false)}
        onCreateNew={() => {
          setCurrentEntityId(null);
          setShowForm(true);
        }}
        onView={(id) => {
          navigate(`/${config.id}/${config.entityName.toLowerCase()}/${id}`);
        }}
        onEdit={(id) => {
          setCurrentEntityId(id);
          setShowForm(true);
        }}
      />

      {!currentEntityId && (config as ListPageConfig).createFormConfig && (
        <Dialog open={showForm} onOpenChange={setShowForm}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogTitle>Create New {config.entityName}</DialogTitle>
            <DynamicFormPage
              config={
                (config as ListPageConfig).createFormConfig as FormPageConfig
              }
              onSuccess={() => {
                setShowForm(false);

                setRefetch(true);

              }}
              onCancel={() => setShowForm(false)}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Edit Entity Dialog */}
      {currentEntityId && (
        <EditEntityDialog
          open={showForm}
          onOpenChange={setShowForm}
          config={config}
          entityId={currentEntityId}
          onSuccess={() => {
            setShowForm(false);

            setRefetch(true);
          }}
        />
      )}
    </>
  );
});

export default DynamicPage;

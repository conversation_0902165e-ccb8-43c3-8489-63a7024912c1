import { z } from "zod";

// Basic types
export const formStatusSchema = z.enum(["draft", "active"]);
export type FormStatus = z.infer<typeof formStatusSchema>;

export const formComponentTypeSchema = z.enum([
  "text",
  "number",
  "date",
  "datetime",
  "select",
  "checkbox",
  "radio",
  "datagrid",
  "step",
  "section",
  "infoText",
]);
export type FormComponentType = z.infer<typeof formComponentTypeSchema>;

export const validationRuleSchema = z.enum([
  "required",
  "min",
  "max",
  "minLength",
  "maxLength",
  "pattern",
  "email",
  "url",
]);
export type ValidationRule = z.infer<typeof validationRuleSchema>;

export const conditionalOperatorSchema = z.enum([
  "equals",
  "notEquals",
  "contains",
  "greaterThan",
  "lessThan",
  "empty",
  "notEmpty",
]);
export type ConditionalOperator = z.infer<typeof conditionalOperatorSchema>;

export const dataGridCellInputTypeSchema = z.enum(["text", "number", "select"]);
export type DataGridCellInputType = z.infer<typeof dataGridCellInputTypeSchema>;

export const dataGridCellTypeSchema = z.enum(["header", "data"]);
export type DataGridCellType = z.infer<typeof dataGridCellTypeSchema>;

// Validation schemas
export const selectOptionSchema = z.object({
  label: z.string(),
  value: z.string(),
});
export type SelectOption = z.infer<typeof selectOptionSchema>;

export const formComponentValidationSchema = z.object({
  rule: validationRuleSchema,
  value: z.any().optional(),
  message: z.string().optional(),
});
export type FormComponentValidation = z.infer<
  typeof formComponentValidationSchema
>;

export const conditionalRenderingSchema = z.object({
  field: z.string(),
  operator: conditionalOperatorSchema,
  value: z.any().optional(),
});
export type ConditionalRendering = z.infer<typeof conditionalRenderingSchema>;

export const gridRowConditionalRenderingSchema = z.object({
  cellId: z.string(),
  operator: conditionalOperatorSchema,
  value: z.any().optional(),
  targetRowIndex: z.number().int().positive(),
});
export type GridRowConditionalRendering = z.infer<
  typeof gridRowConditionalRenderingSchema
>;

// Form component schemas
export const formComponentBaseSchema = z.object({
  id: z.string(),
  type: formComponentTypeSchema,
  label: z.string(),
  name: z.string(),
  required: z.boolean().optional(),
  placeholder: z.string().optional(),
  defaultValue: z.any().optional(),
  validations: z.array(formComponentValidationSchema).optional(),
  conditionalRendering: conditionalRenderingSchema.optional(),
  parentId: z.string().optional(),
});

export const textComponentSchema = formComponentBaseSchema.extend({
  type: z.literal("text"),
  minLength: z.number().int().min(0).optional(),
  maxLength: z.number().int().min(0).optional(),
});
export type TextComponent = z.infer<typeof textComponentSchema>;

export const numberComponentSchema = formComponentBaseSchema.extend({
  type: z.literal("number"),
  min: z.number().optional(),
  max: z.number().optional(),
  step: z.number().optional(),
  unit: z.string().optional(),
});
export type NumberComponent = z.infer<typeof numberComponentSchema>;

export const dateComponentSchema = formComponentBaseSchema.extend({
  type: z.literal("date"),
  min: z.string().optional(),
  max: z.string().optional(),
});
export type DateComponent = z.infer<typeof dateComponentSchema>;

export const dateTimeComponentSchema = formComponentBaseSchema.extend({
  type: z.literal("datetime"),
  min: z.string().optional(),
  max: z.string().optional(),
});
export type DateTimeComponent = z.infer<typeof dateTimeComponentSchema>;

export const selectComponentSchema = formComponentBaseSchema.extend({
  type: z.literal("select"),
  options: z.array(selectOptionSchema),
  multiple: z.boolean().optional(),
});
export type SelectComponent = z.infer<typeof selectComponentSchema>;

export const checkboxComponentSchema = formComponentBaseSchema.extend({
  type: z.literal("checkbox"),
  options: z.array(selectOptionSchema).optional(),
  multiple: z.boolean().optional(),
});
export type CheckboxComponent = z.infer<typeof checkboxComponentSchema>;

export const radioComponentSchema = formComponentBaseSchema.extend({
  type: z.literal("radio"),
  options: z.array(selectOptionSchema),
});
export type RadioComponent = z.infer<typeof radioComponentSchema>;

export const dataGridCellSchema = z.object({
  id: z.string(),
  value: z.string(),
  type: dataGridCellTypeSchema,
  inputType: dataGridCellInputTypeSchema.optional(),
  options: z.array(selectOptionSchema).optional(),
  validations: z.array(formComponentValidationSchema).optional(),
  unit: z.string().optional(),
  min: z.number().optional(),
  max: z.number().optional(),
  step: z.number().optional(),
  required: z.boolean().optional(),
});
export type DataGridCell = z.infer<typeof dataGridCellSchema>;

export const dataGridColumnConfigSchema = z.object({
  columnIndex: z.number().int().min(0),
  inputType: dataGridCellInputTypeSchema,
  options: z.array(selectOptionSchema).optional(),
  validations: z.array(formComponentValidationSchema).optional(),
  unit: z.string().optional(),
  min: z.number().optional(),
  max: z.number().optional(),
  step: z.number().optional(),
});
export type DataGridColumnConfig = z.infer<typeof dataGridColumnConfigSchema>;

export const dataGridComponentSchema = formComponentBaseSchema.extend({
  type: z.literal("datagrid"),
  rows: z.number().int().min(1),
  columns: z.number().int().min(1),
  cells: z.record(z.string(), dataGridCellSchema),
  columnConfigs: z.record(z.string(), dataGridColumnConfigSchema).optional(),
  conditionalRows: z.array(gridRowConditionalRenderingSchema).optional(),
});
export type DataGridComponent = z.infer<typeof dataGridComponentSchema>;

export const stepComponentSchema = formComponentBaseSchema.extend({
  type: z.literal("step"),
  description: z.string().optional(),
  icon: z.string().optional(),
  children: z.array(z.string()).optional(),
});
export type StepComponent = z.infer<typeof stepComponentSchema>;

export const sectionComponentSchema = formComponentBaseSchema.extend({
  type: z.literal("section"),
  description: z.string().optional(),
  collapsible: z.boolean().optional(),
  defaultCollapsed: z.boolean().optional(),
  children: z.array(z.string()).optional(),
});
export type SectionComponent = z.infer<typeof sectionComponentSchema>;

export const infoTextComponentSchema = formComponentBaseSchema.extend({
  type: z.literal("infoText"),
  infoContent: z.string(),
  variant: z.enum(["default", "info", "warning", "success"]).optional(),
});
export type InfoTextComponent = z.infer<typeof infoTextComponentSchema>;

// Union of all component types
export const formComponentSchema = z.discriminatedUnion("type", [
  textComponentSchema,
  numberComponentSchema,
  dateComponentSchema,
  dateTimeComponentSchema,
  selectComponentSchema,
  checkboxComponentSchema,
  radioComponentSchema,
  dataGridComponentSchema,
  stepComponentSchema,
  sectionComponentSchema,
  infoTextComponentSchema,
]);
export type FormComponent = z.infer<typeof formComponentSchema>;

// Form schema
export const formSchemaSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  status: formStatusSchema,
  components: z.array(formComponentSchema),
  createdAt: z.string(),
  updatedAt: z.string(),
});
export type FormSchema = z.infer<typeof formSchemaSchema>;

// Form meta schema
export const formMetaSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  status: formStatusSchema,
  createdAt: z.string(),
  updatedAt: z.string(),
});
export type FormMeta = z.infer<typeof formMetaSchema>;

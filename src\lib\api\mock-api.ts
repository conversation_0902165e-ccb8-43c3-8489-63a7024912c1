import { FormMeta, FormSchema, FormStatus } from "@/lib/types/form";
import {
  FormSubmission,
  SubmissionMeta,
  SubmissionStatus,
} from "@/lib/types/submission";
import { delay } from "@/lib/utils/delay";
import { mockForms, mockFormSchemas } from "@/lib/mocks/form-mocks";
import { mockSubmissions } from "@/lib/mocks/submission-mocks";

/**
 * Mock API service for development
 * This simulates the backend API until it's ready
 */
export const mockApi = {
  /**
   * Get all forms with optional filtering
   */
  getForms: async (status?: string): Promise<FormMeta[]> => {
    await delay(500); // Simulate network delay

    if (status) {
      return mockForms.filter((form) => form.status === status);
    }

    return [...mockForms];
  },

  /**
   * Get a specific form by ID
   */
  getFormById: async (id: string): Promise<FormSchema | null> => {
    await delay(300);

    return mockFormSchemas[id] || null;
  },

  /**
   * Create a new form
   */
  createForm: async (formData: Partial<FormSchema>): Promise<FormSchema> => {
    await delay(800);

    // Generate a new form with the provided data
    const newForm: FormSchema = {
      id: `form-${Date.now()}`,
      name: formData.name || "New Form",
      description: formData.description || "",
      status: "draft",
      components: formData.components || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      // No metadata field in FormSchema
    };

    // Add to mock data (in a real app, this would be persisted to a database)
    mockFormSchemas[newForm.id] = newForm;
    mockForms.push({
      id: newForm.id,
      name: newForm.name,
      description: newForm.description,
      status: newForm.status,
      createdAt: newForm.createdAt,
      updatedAt: newForm.updatedAt,
    });

    return newForm;
  },

  /**
   * Update an existing form
   */
  updateForm: async (
    id: string,
    formData: Partial<FormSchema>
  ): Promise<FormSchema> => {
    await delay(500);

    const existingForm = mockFormSchemas[id];
    if (!existingForm) {
      throw new Error(`Form with ID ${id} not found`);
    }

    // Update the form
    const updatedForm: FormSchema = {
      ...existingForm,
      ...formData,
      updatedAt: new Date().toISOString(),
    };

    // Update in mock data
    mockFormSchemas[id] = updatedForm;

    // Also update in forms list
    const formIndex = mockForms.findIndex((form) => form.id === id);
    if (formIndex !== -1) {
      mockForms[formIndex] = {
        id: updatedForm.id,
        name: updatedForm.name,
        description: updatedForm.description,
        status: updatedForm.status,
        createdAt: updatedForm.createdAt,
        updatedAt: updatedForm.updatedAt,
      };
    }

    return updatedForm;
  },

  /**
   * Delete a form
   */
  deleteForm: async (id: string): Promise<void> => {
    await delay(500);

    // Check if form exists
    if (!mockFormSchemas[id]) {
      throw new Error(`Form with ID ${id} not found`);
    }

    // Delete from mock data
    delete mockFormSchemas[id];

    // Also delete from forms list
    const formIndex = mockForms.findIndex((form) => form.id === id);
    if (formIndex !== -1) {
      mockForms.splice(formIndex, 1);
    }
  },

  /**
   * Get all submissions with optional filtering
   */
  getSubmissions: async (
    status?: string,
    applicantId?: string
  ): Promise<SubmissionMeta[]> => {
    await delay(500);

    let filteredSubmissions = [...mockSubmissions];

    if (status) {
      filteredSubmissions = filteredSubmissions.filter(
        (submission) => submission.status === status
      );
    }

    if (applicantId) {
      filteredSubmissions = filteredSubmissions.filter(
        (submission) => submission.applicantId === applicantId
      );
    }

    return filteredSubmissions;
  },

  /**
   * Get a specific submission by ID
   */
  getSubmissionById: async (id: string): Promise<FormSubmission | null> => {
    await delay(300);

    const submission = mockSubmissions.find((sub) => sub.id === id);
    if (!submission) {
      return null;
    }

    // In a real app, this would fetch the full submission data
    // For now, we'll just return a mock submission with minimal data
    return {
      id: submission.id,
      formId: submission.formId,
      formSchema: mockFormSchemas[submission.formId] || {
        id: submission.formId,
        name: "Unknown Form",
        description: "",
        status: "active",
        components: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      applicantId: submission.applicantId,
      applicant: {
        id: submission.applicantId,
        name: "Mock User",
        email: "<EMAIL>",
        role: "applicant",
      },
      status: submission.status,
      data: {}, // Mock data
      createdAt: submission.createdAt,
      updatedAt: submission.updatedAt,
      submittedAt: submission.submittedAt || undefined,
    };
  },

  /**
   * Create a new submission
   */
  createSubmission: async (
    submissionData: Partial<FormSubmission>
  ): Promise<FormSubmission> => {
    await delay(800);

    // Generate a new submission with the provided data
    // Create a simplified submission without all FormSubmission fields
    const newSubmissionData = {
      id: `submission-${Date.now()}`,
      formId: submissionData.formId || "",
      applicantId: submissionData.applicantId || "",
      status: "draft" as SubmissionStatus,
      data: submissionData.data || {},
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      submittedAt: undefined,
    };

    // Add to mock data with required fields for SubmissionMeta
    mockSubmissions.push({
      id: newSubmissionData.id,
      formId: newSubmissionData.formId,
      formName: "Mock Form",
      applicantId: newSubmissionData.applicantId,
      applicantName: "Mock User",
      status: newSubmissionData.status,
      createdAt: newSubmissionData.createdAt,
      updatedAt: newSubmissionData.updatedAt,
      submittedAt: newSubmissionData.submittedAt,
    });

    // Return a FormSubmission with all required fields
    return {
      ...newSubmissionData,
      formSchema: mockFormSchemas[newSubmissionData.formId] || {
        id: newSubmissionData.formId,
        name: "Unknown Form",
        description: "",
        status: "active" as FormStatus,
        components: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      applicant: {
        id: newSubmissionData.applicantId,
        name: "Mock User",
        email: "<EMAIL>",
        role: "applicant",
      },
    };
  },

  /**
   * Update an existing submission
   */
  updateSubmission: async (
    id: string,
    submissionData: Partial<FormSubmission>
  ): Promise<FormSubmission> => {
    await delay(500);

    const submissionIndex = mockSubmissions.findIndex((sub) => sub.id === id);
    if (submissionIndex === -1) {
      throw new Error(`Submission with ID ${id} not found`);
    }

    // Update the submission
    const existingSubmission = mockSubmissions[submissionIndex];
    // Create updated submission data
    const updatedSubmissionData = {
      id,
      formId: existingSubmission.formId,
      applicantId: existingSubmission.applicantId,
      status: submissionData.status || existingSubmission.status,
      data: submissionData.data || {},
      createdAt: existingSubmission.createdAt,
      updatedAt: new Date().toISOString(),
      submittedAt: existingSubmission.submittedAt,
    };

    // Update in mock data with required fields for SubmissionMeta
    mockSubmissions[submissionIndex] = {
      id: updatedSubmissionData.id,
      formId: updatedSubmissionData.formId,
      formName: existingSubmission.formName,
      applicantId: updatedSubmissionData.applicantId,
      applicantName: existingSubmission.applicantName,
      status: updatedSubmissionData.status,
      createdAt: updatedSubmissionData.createdAt,
      updatedAt: updatedSubmissionData.updatedAt,
      submittedAt: updatedSubmissionData.submittedAt,
    };

    // Return a FormSubmission with all required fields
    return {
      ...updatedSubmissionData,
      formSchema: mockFormSchemas[updatedSubmissionData.formId] || {
        id: updatedSubmissionData.formId,
        name: "Unknown Form",
        description: "",
        status: "active" as FormStatus,
        components: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      applicant: {
        id: updatedSubmissionData.applicantId,
        name: existingSubmission.applicantName || "Mock User",
        email: "<EMAIL>",
        role: "applicant",
      },
    };
  },

  /**
   * Delete a submission
   */
  deleteSubmission: async (id: string): Promise<void> => {
    await delay(500);

    const submissionIndex = mockSubmissions.findIndex((sub) => sub.id === id);
    if (submissionIndex === -1) {
      throw new Error(`Submission with ID ${id} not found`);
    }

    // Delete from mock data
    mockSubmissions.splice(submissionIndex, 1);
  },
};

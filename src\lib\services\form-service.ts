import { FormMeta, FormSchema, FormStatus } from "../schemas/form-schemas";

// Mock data for forms
const mockForms: FormMeta[] = [
  {
    id: "1",
    name: "Customer Feedback Form",
    description: "Form to collect customer feedback about our services",
    status: "active",
    createdAt: "2023-01-15T10:30:00Z",
    updatedAt: "2023-02-20T14:45:00Z",
  },
  {
    id: "2",
    name: "Employee Onboarding",
    description: "New employee information collection form",
    status: "active",
    createdAt: "2023-03-05T09:15:00Z",
    updatedAt: "2023-03-10T11:20:00Z",
  },
  {
    id: "3",
    name: "Project Request Form",
    description: "Form to request new project creation",
    status: "draft",
    createdAt: "2023-04-18T13:40:00Z",
    updatedAt: "2023-04-18T13:40:00Z",
  },
  {
    id: "4",
    name: "Bug Report Template",
    description: "Template for reporting software bugs",
    status: "draft",
    createdAt: "2023-05-22T16:10:00Z",
    updatedAt: "2023-05-25T09:30:00Z",
  },
  {
    id: "5",
    name: "Form Builder Showcase",
    description:
      "A comprehensive example demonstrating all form builder capabilities",
    status: "active",
    createdAt: "2023-06-10T08:00:00Z",
    updatedAt: "2023-06-15T14:30:00Z",
  },
];

// Mock form schemas
const mockFormSchemas: Record<string, FormSchema> = {
  "1": {
    id: "1",
    name: "Customer Feedback Form",
    description: "Form to collect customer feedback about our services",
    status: "active",
    components: [
      {
        id: "name",
        type: "text",
        label: "Full Name",
        name: "fullName",
        required: true,
        placeholder: "Enter your full name",
      },
      {
        id: "email",
        type: "text",
        label: "Email Address",
        name: "email",
        required: true,
        placeholder: "Enter your email address",
        validations: [
          {
            rule: "email",
            message: "Please enter a valid email address",
          },
        ],
      },
      {
        id: "rating",
        type: "select",
        label: "How would you rate our service?",
        name: "rating",
        required: true,
        options: [
          { label: "Excellent", value: "5" },
          { label: "Good", value: "4" },
          { label: "Average", value: "3" },
          { label: "Below Average", value: "2" },
          { label: "Poor", value: "1" },
        ],
      },
      {
        id: "comments",
        type: "text",
        label: "Additional Comments",
        name: "comments",
        placeholder: "Please share any additional feedback",
        minLength: 10,
        maxLength: 500,
      },
    ],
    createdAt: "2023-01-15T10:30:00Z",
    updatedAt: "2023-02-20T14:45:00Z",
  },
  "5": {
    id: "5",
    name: "Form Builder Showcase",
    description:
      "A comprehensive example demonstrating all form builder capabilities",
    status: "active",
    components: [
      // Step 1: Personal Information
      {
        id: "step1",
        type: "step",
        label: "Personal Information",
        name: "personalInfo",
        description: "Basic personal and contact information",
        children: [],
      },
      {
        id: "personalSection",
        type: "section",
        label: "Basic Details",
        name: "basicDetails",
        description: "Your personal identification information",
        parentId: "step1",
        collapsible: true,
        defaultCollapsed: false,
        children: [],
      },
      {
        id: "fullName",
        type: "text",
        label: "Full Name",
        name: "fullName",
        required: true,
        placeholder: "Enter your full name",
        parentId: "personalSection",
        validations: [
          {
            rule: "required",
            message: "Full name is required",
          },
          {
            rule: "minLength",
            value: 3,
            message: "Name must be at least 3 characters",
          },
        ],
      },
      {
        id: "email",
        type: "text",
        label: "Email Address",
        name: "email",
        required: true,
        placeholder: "Enter your email address",
        parentId: "personalSection",
        validations: [
          {
            rule: "required",
            message: "Email is required",
          },
          {
            rule: "email",
            message: "Please enter a valid email address",
          },
        ],
      },
      {
        id: "contactSection",
        type: "section",
        label: "Contact Information",
        name: "contactInfo",
        description: "How we can reach you",
        parentId: "step1",
        collapsible: true,
        defaultCollapsed: false,
        children: [],
      },
      {
        id: "phone",
        type: "text",
        label: "Phone Number",
        name: "phone",
        placeholder: "Enter your phone number",
        parentId: "contactSection",
        validations: [
          {
            rule: "pattern",
            value: "^[0-9]{10}$",
            message: "Please enter a valid 10-digit phone number",
          },
        ],
      },
      {
        id: "preferredContact",
        type: "radio",
        label: "Preferred Contact Method",
        name: "preferredContact",
        parentId: "contactSection",
        options: [
          { label: "Email", value: "email" },
          { label: "Phone", value: "phone" },
          { label: "Text Message", value: "text" },
        ],
      },

      // Step 2: Professional Information
      {
        id: "step2",
        type: "step",
        label: "Professional Information",
        name: "professionalInfo",
        description: "Your work experience and skills",
        children: [],
      },
      {
        id: "experienceSection",
        type: "section",
        label: "Work Experience",
        name: "workExperience",
        description: "Tell us about your professional background",
        parentId: "step2",
        collapsible: true,
        defaultCollapsed: false,
        children: [],
      },
      {
        id: "yearsExperience",
        type: "number",
        label: "Years of Experience",
        name: "yearsExperience",
        required: true,
        placeholder: "Enter years of experience",
        parentId: "experienceSection",
        min: 0,
        max: 50,
        step: 1,
        validations: [
          {
            rule: "required",
            message: "Years of experience is required",
          },
          {
            rule: "min",
            value: 0,
            message: "Years cannot be negative",
          },
          {
            rule: "max",
            value: 50,
            message: "Years cannot exceed 50",
          },
        ],
      },
      {
        id: "currentTitle",
        type: "text",
        label: "Current Job Title",
        name: "currentTitle",
        placeholder: "Enter your current job title",
        parentId: "experienceSection",
      },
      {
        id: "startDate",
        type: "date",
        label: "Employment Start Date",
        name: "startDate",
        parentId: "experienceSection",
        conditionalRendering: {
          field: "currentTitle",
          operator: "notEquals",
          value: "",
        },
      },
      {
        id: "skillsSection",
        type: "section",
        label: "Skills & Expertise",
        name: "skillsExpertise",
        description: "Your professional skills and expertise areas",
        parentId: "step2",
        collapsible: true,
        defaultCollapsed: false,
        children: [],
      },
      {
        id: "skillLevel",
        type: "select",
        label: "Skill Level",
        name: "skillLevel",
        parentId: "skillsSection",
        options: [
          { label: "Beginner", value: "beginner" },
          { label: "Intermediate", value: "intermediate" },
          { label: "Advanced", value: "advanced" },
          { label: "Expert", value: "expert" },
        ],
      },
      {
        id: "expertise",
        type: "checkbox",
        label: "Areas of Expertise",
        name: "expertise",
        parentId: "skillsSection",
        options: [
          { label: "Frontend Development", value: "frontend" },
          { label: "Backend Development", value: "backend" },
          { label: "Database Design", value: "database" },
          { label: "UI/UX Design", value: "uiux" },
          { label: "DevOps", value: "devops" },
        ],
      },
      // Step 4: Data Grid Example
      {
        id: "step3",
        type: "step",
        label: "Resource Allocation",
        name: "resourceAllocation",
        description: "Allocate resources for your project",
        children: [],
      },
      {
        id: "resourceGrid",
        type: "datagrid",
        label: "Resource Allocation Matrix",
        name: "resourceMatrix",
        parentId: "step3",
        rows: 5,
        columns: 5,
        cells: {
          // Headers
          A1: {
            id: "A1",
            value: "Resource Type",
            type: "header",
            validations: [],
          },
          B1: { id: "B1", value: "Quantity", type: "header", validations: [] },
          C1: {
            id: "C1",
            value: "Cost per Unit",
            type: "header",
            validations: [],
          },
          D1: {
            id: "D1",
            value: "Total Cost",
            type: "header",
            validations: [],
          },
          E1: { id: "E1", value: "Priority", type: "header", validations: [] },

          // Row headers
          A2: {
            id: "A2",
            value: "Developers",
            type: "header",
            validations: [],
          },
          A3: { id: "A3", value: "Designers", type: "header", validations: [] },
          A4: {
            id: "A4",
            value: "QA Engineers",
            type: "header",
            validations: [],
          },
          A5: {
            id: "A5",
            value: "Project Managers",
            type: "header",
            validations: [],
          },

          // Data cells
          B2: {
            id: "B2",
            value: "4",
            type: "data",
            inputType: "number",
            min: 0,
            max: 20,
            step: 1,
            validations: [],
          },
          B3: {
            id: "B3",
            value: "2",
            type: "data",
            inputType: "number",
            min: 0,
            max: 10,
            step: 1,
            validations: [],
          },
          B4: {
            id: "B4",
            value: "2",
            type: "data",
            inputType: "number",
            min: 0,
            max: 10,
            step: 1,
            validations: [],
          },
          B5: {
            id: "B5",
            value: "1",
            type: "data",
            inputType: "number",
            min: 0,
            max: 5,
            step: 1,
            validations: [],
          },

          C2: {
            id: "C2",
            value: "100",
            type: "data",
            inputType: "number",
            unit: "$",
            validations: [],
          },
          C3: {
            id: "C3",
            value: "90",
            type: "data",
            inputType: "number",
            unit: "$",
            validations: [],
          },
          C4: {
            id: "C4",
            value: "80",
            type: "data",
            inputType: "number",
            unit: "$",
            validations: [],
          },
          C5: {
            id: "C5",
            value: "120",
            type: "data",
            inputType: "number",
            unit: "$",
            validations: [],
          },

          D2: {
            id: "D2",
            value: "400",
            type: "data",
            inputType: "number",
            unit: "$",
            validations: [],
          },
          D3: {
            id: "D3",
            value: "180",
            type: "data",
            inputType: "number",
            unit: "$",
            validations: [],
          },
          D4: {
            id: "D4",
            value: "160",
            type: "data",
            inputType: "number",
            unit: "$",
            validations: [],
          },
          D5: {
            id: "D5",
            value: "120",
            type: "data",
            inputType: "number",
            unit: "$",
            validations: [],
          },

          E2: {
            id: "E2",
            value: "high",
            type: "data",
            inputType: "select",
            options: [
              { label: "High", value: "high" },
              { label: "Medium", value: "medium" },
              { label: "Low", value: "low" },
            ],
            validations: [],
          },
          E3: {
            id: "E3",
            value: "high",
            type: "data",
            inputType: "select",
            options: [
              { label: "High", value: "high" },
              { label: "Medium", value: "medium" },
              { label: "Low", value: "low" },
            ],
            validations: [],
          },
          E4: {
            id: "E4",
            value: "medium",
            type: "data",
            inputType: "select",
            options: [
              { label: "High", value: "high" },
              { label: "Medium", value: "medium" },
              { label: "Low", value: "low" },
            ],
            validations: [],
          },
          E5: {
            id: "E5",
            value: "medium",
            type: "data",
            inputType: "select",
            options: [
              { label: "High", value: "high" },
              { label: "Medium", value: "medium" },
              { label: "Low", value: "low" },
            ],
            validations: [],
          },
        },
      },
      // Step 3: Project Details
      {
        id: "step4",
        type: "step",
        label: "Project Details",
        name: "projectDetails",
        description: "Information about your project requirements",
        children: [],
      },
      {
        id: "projectSection",
        type: "section",
        label: "Project Information",
        name: "projectInfo",
        description: "Basic details about your project",
        parentId: "step4",
        collapsible: true,
        defaultCollapsed: false,
        children: [],
      },
      {
        id: "projectName",
        type: "text",
        label: "Project Name",
        name: "projectName",
        required: true,
        placeholder: "Enter project name",
        parentId: "projectSection",
      },
      {
        id: "projectType",
        type: "select",
        label: "Project Type",
        name: "projectType",
        parentId: "projectSection",
        options: [
          { label: "Web Application", value: "web" },
          { label: "Mobile Application", value: "mobile" },
          { label: "Desktop Application", value: "desktop" },
          { label: "API/Service", value: "api" },
          { label: "Other", value: "other" },
        ],
      },
      {
        id: "projectDescription",
        type: "text",
        label: "Project Description",
        name: "projectDescription",
        placeholder: "Describe your project",
        parentId: "projectSection",
        minLength: 20,
        maxLength: 500,
      },
      {
        id: "budgetSection",
        type: "section",
        label: "Budget & Timeline",
        name: "budgetTimeline",
        description: "Financial and scheduling information",
        parentId: "step3",
        collapsible: true,
        defaultCollapsed: false,
        children: [],
      },
      {
        id: "budget",
        type: "number",
        label: "Estimated Budget",
        name: "budget",
        placeholder: "Enter budget amount",
        parentId: "budgetSection",
        min: 1000,
        step: 1000,
        unit: "$",
      },
      {
        id: "timeline",
        type: "select",
        label: "Expected Timeline",
        name: "timeline",
        parentId: "budgetSection",
        options: [
          { label: "Less than 1 month", value: "lt1m" },
          { label: "1-3 months", value: "1-3m" },
          { label: "3-6 months", value: "3-6m" },
          { label: "6-12 months", value: "6-12m" },
          { label: "More than 12 months", value: "gt12m" },
        ],
      },
      {
        id: "startDateProject",
        type: "date",
        label: "Desired Start Date",
        name: "startDateProject",
        parentId: "budgetSection",
        min: new Date().toISOString().split("T")[0], // Today's date
      },
    ],
    createdAt: "2023-06-10T08:00:00Z",
    updatedAt: "2023-06-15T14:30:00Z",
  },
  // Add more mock schemas as needed
};

// Simulate API delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export const FormService = {
  // Get all forms with optional filtering
  getForms: async (status?: FormStatus): Promise<FormMeta[]> => {
    await delay(500); // Simulate network delay

    if (status) {
      return mockForms.filter((form) => form.status === status);
    }

    return [...mockForms];
  },

  // Get a specific form by ID
  getFormById: async (id: string): Promise<FormSchema | null> => {
    await delay(300);

    return mockFormSchemas[id] || null;
  },

  // Create a new form
  createForm: async (
    form: Omit<FormSchema, "id" | "createdAt" | "updatedAt">
  ): Promise<FormSchema> => {
    await delay(700);

    const now = new Date().toISOString();
    const newId = (
      Math.max(...mockForms.map((f) => parseInt(f.id))) + 1
    ).toString();

    const newForm: FormSchema = {
      ...form,
      id: newId,
      createdAt: now,
      updatedAt: now,
    };

    // Add to mock data
    mockForms.push({
      id: newId,
      name: form.name,
      description: form.description,
      status: form.status,
      createdAt: now,
      updatedAt: now,
    });

    mockFormSchemas[newId] = newForm;

    return newForm;
  },

  // Update an existing form
  updateForm: async (
    id: string,
    form: Partial<FormSchema>
  ): Promise<FormSchema> => {
    await delay(500);

    const existingForm = mockFormSchemas[id];

    if (!existingForm) {
      throw new Error(`Form with ID ${id} not found`);
    }

    const now = new Date().toISOString();

    const updatedForm: FormSchema = {
      ...existingForm,
      ...form,
      updatedAt: now,
    };

    // Update mock data
    mockFormSchemas[id] = updatedForm;

    // Update form meta
    const formIndex = mockForms.findIndex((f) => f.id === id);
    if (formIndex !== -1) {
      mockForms[formIndex] = {
        ...mockForms[formIndex],
        name: updatedForm.name,
        description: updatedForm.description,
        status: updatedForm.status,
        updatedAt: now,
      };
    }

    return updatedForm;
  },

  // Delete a form
  deleteForm: async (id: string): Promise<boolean> => {
    await delay(400);

    const formIndex = mockForms.findIndex((f) => f.id === id);

    if (formIndex === -1) {
      return false;
    }

    // Remove from mock data
    mockForms.splice(formIndex, 1);
    delete mockFormSchemas[id];

    return true;
  },
};

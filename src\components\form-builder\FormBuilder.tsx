import { memo, useMemo } from "react";
import { FormComponent } from "@/lib/schemas/form-schemas";

import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from "@dnd-kit/core";
import { sortableKeyboardCoordinates } from "@dnd-kit/sortable";

// Import custom hooks and components
import { useFormBuilder } from "@/hooks/useFormBuilder";
import EmptyFormArea from "./EmptyFormArea";
import ComponentList from "./ComponentList";
import FormBuilderSidebar from "./FormBuilderSidebar";
import DragOverlayContent from "./DragOverlayContent";

interface FormBuilderProps {
  readonly components: FormComponent[];
  readonly onChange: (components: FormComponent[]) => void;
  readonly useDndContext?: boolean; // Optional prop to control whether to use internal DndContext
}

function FormBuilder({
  components,
  onChange,
  useDndContext = true, // Default to using internal DndContext
}: FormBuilderProps) {
  // Use our custom hook for form builder state management
  const {
    selectedComponentId,
    selectedComponent,
    activeId,
    expandedContainers,
    sidebarTab,
    handleToggleExpand,
    handleAddComponent,
    handleSelectComponent,
    handleUpdateComponent,
    handleDeleteComponent,
    handleMoveComponent,
    handleDragStart,
    handleDragEnd,
    setSelectedComponentId,
    setSidebarTab,
  } = useFormBuilder({ components, onChange });

  // Set up sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Memoize the form content to prevent unnecessary re-renders
  const formContent = useMemo(
    () => (
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <div className="md:col-span-2 flex flex-col">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-lg font-medium">Form Components</h2>
          </div>

          <div className="h-[calc(100vh-200px)] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
            {components.length === 0 ? (
              <EmptyFormArea
                onAddComponent={() => setSelectedComponentId(null)}
              />
            ) : (
              <ComponentList
                components={components}
                selectedComponentId={selectedComponentId}
                expandedContainers={expandedContainers}
                onSelectComponent={handleSelectComponent}
                onDeleteComponent={handleDeleteComponent}
                onMoveComponent={handleMoveComponent}
                onToggleExpand={handleToggleExpand}
              />
            )}
          </div>
        </div>

        <div className="form-builder-sidebar h-[calc(100vh-200px)] sticky top-4 flex flex-col">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-lg font-medium">Component Options</h2>
          </div>
          <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent pr-2">
            <FormBuilderSidebar
              selectedComponent={selectedComponent}
              selectedComponentId={selectedComponentId}
              allComponents={components}
              onAddComponent={handleAddComponent}
              onUpdateComponent={handleUpdateComponent}
              onDeleteComponent={handleDeleteComponent}
              activeTab={sidebarTab}
              onTabChange={setSidebarTab}
            />
          </div>
        </div>
      </div>
    ),
    [
      components,
      selectedComponentId,
      selectedComponent,
      expandedContainers,
      sidebarTab,
      handleSelectComponent,
      handleDeleteComponent,
      handleMoveComponent,
      handleToggleExpand,
      handleAddComponent,
      handleUpdateComponent,
      setSelectedComponentId,
      setSidebarTab,
    ]
  );

  // Conditionally wrap with DndContext based on the useDndContext prop
  return useDndContext ? (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      {formContent}
      <DragOverlay>
        <DragOverlayContent activeId={activeId} components={components} />
      </DragOverlay>
    </DndContext>
  ) : (
    // If not using internal DndContext, just render the content
    // The parent component is responsible for providing the DndContext
    formContent
  );
}

export default memo(FormBuilder);
